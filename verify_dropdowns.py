import openpyxl
from openpyxl.utils import get_column_letter

def verify_dropdowns_in_file(filename):
    """Verify that dropdowns exist in the Excel file"""
    try:
        workbook = openpyxl.load_workbook(filename)
        sheet = workbook["Raw Data"]
        
        print(f"Checking dropdowns in file: {filename}")
        print(f"Sheet name: {sheet.title}")
        print(f"Data validations count: {len(sheet.data_validations.dataValidation)}")
        
        # Check each data validation
        for i, dv in enumerate(sheet.data_validations.dataValidation):
            print(f"\nData Validation {i+1}:")
            print(f"  Type: {dv.type}")
            print(f"  Formula1: {dv.formula1}")
            print(f"  Allow blank: {dv.allowBlank}")
            print(f"  Ranges: {[str(r) for r in dv.ranges]}")
            print(f"  Error title: {dv.errorTitle}")
            print(f"  Error message: {dv.error}")
        
        # Check specific cells for validation
        test_cells = ['A2', 'I2', 'J2', 'L2', 'M2', 'N2', 'O2']
        print(f"\nChecking specific cells for validation:")
        for cell_ref in test_cells:
            cell = sheet[cell_ref]
            has_validation = any(cell_ref in str(dv.ranges) for dv in sheet.data_validations.dataValidation)
            print(f"  {cell_ref}: {'Has validation' if has_validation else 'No validation'}")
        
        workbook.close()
        
    except Exception as e:
        print(f"Error checking file: {e}")

def create_minimal_dropdown_test():
    """Create the most minimal dropdown test possible"""
    import pandas as pd
    
    # Create minimal data
    data = {
        'Scope': ['Test1', 'Test2'],
        'Error(Y/N)': ['Yes', 'No']
    }
    
    df = pd.DataFrame(data)
    df.to_excel('minimal_test.xlsx', sheet_name="Raw Data", index=False)
    
    # Add dropdown manually
    workbook = openpyxl.load_workbook('minimal_test.xlsx')
    sheet = workbook["Raw Data"]
    
    # Add dropdown to Scope column (A2:A3)
    from openpyxl.worksheet.datavalidation import DataValidation
    dv = DataValidation(type="list", formula1='"In Scope,Out of Scope"', allow_blank=True)
    sheet.add_data_validation(dv)
    dv.add('A2:A3')
    
    # Add dropdown to Error(Y/N) column (B2:B3)
    dv2 = DataValidation(type="list", formula1='"Yes,No"', allow_blank=True)
    sheet.add_data_validation(dv2)
    dv2.add('B2:B3')
    
    workbook.save('minimal_test.xlsx')
    workbook.close()
    
    print("Created minimal_test.xlsx")
    return 'minimal_test.xlsx'

if __name__ == "__main__":
    # Test the simple test file
    print("=== Checking simple_test_output.xlsx ===")
    verify_dropdowns_in_file('simple_test_output.xlsx')
    
    # Create and test minimal file
    print("\n=== Creating and checking minimal test ===")
    minimal_file = create_minimal_dropdown_test()
    verify_dropdowns_in_file(minimal_file)
