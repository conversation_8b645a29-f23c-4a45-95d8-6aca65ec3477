def add_qc_dropdown_functionality(worksheet, full_data, qc_start_col, qc_start_row, data_start_row, period_data_size, period_type):
    """Add dropdown functionality to QC Summary section"""
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    from openpyxl.utils import get_column_letter
    import pandas as pd
    
    # Define styles
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center')
    
    # Find QC column
    qc_col = None
    for col in full_data.columns:
        if 'qc' in str(col).lower():
            qc_col = col
            break
    
    # QC Summary title
    worksheet.merge_cells(start_row=qc_start_row, start_column=qc_start_col, 
                         end_row=qc_start_row, end_column=qc_start_col+1)
    qc_title = worksheet.cell(row=qc_start_row, column=qc_start_col, value="QC Summary")
    qc_title.font = Font(bold=True, size=12)
    qc_title.fill = title_fill
    qc_title.alignment = center_alignment
    qc_title.border = thin_border

    # QC dropdown for period selection
    qc_dropdown_cell = worksheet.cell(row=qc_start_row+1, column=qc_start_col, value=period_type)
    qc_dropdown_cell.font = Font(bold=True, size=10)
    qc_dropdown_cell.fill = dropdown_fill
    qc_dropdown_cell.alignment = center_alignment
    qc_dropdown_cell.border = thin_border
    
    qc_dv = DataValidation(type="list", formula1='"Daily,Weekly,Monthly"', allow_blank=False)
    qc_dv.add(qc_dropdown_cell)
    worksheet.add_data_validation(qc_dv)

    # QC headers
    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").border = thin_border
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").border = thin_border

    if qc_col:
        # Create QC data for all periods and store in hidden rows
        qc_data_start_row = data_start_row + period_data_size + 10
        
        # Daily QC data
        daily_qc_data = full_data[qc_col].dropna().astype(str)
        daily_qc_data = daily_qc_data[daily_qc_data.str.strip() != '']
        daily_qc_data = daily_qc_data[daily_qc_data != 'nan']
        
        daily_qc_counts = pd.DataFrame()
        if len(daily_qc_data) > 0:
            daily_qc_counts = daily_qc_data.value_counts().reset_index()
            daily_qc_counts.columns = ['QC Name', 'Count']
            
            # Store daily QC data in hidden rows
            for i, qc_row in daily_qc_counts.iterrows():
                worksheet.cell(row=qc_data_start_row+1+i, column=qc_start_col+3, value=str(qc_row['QC Name']))
                worksheet.cell(row=qc_data_start_row+1+i, column=qc_start_col+4, value=int(qc_row['Count']))
        
        # Weekly QC data
        weekly_qc_start = qc_data_start_row + 15
        full_data_copy = full_data.copy()
        full_data_copy['Week'] = full_data_copy['Audit Date'].dt.to_period('W')
        weekly_qc_data = full_data_copy.groupby('Week')[qc_col].apply(lambda x: x.dropna().astype(str).value_counts()).reset_index()
        
        weekly_qc_counts = pd.DataFrame()
        if len(weekly_qc_data) > 0:
            weekly_qc_counts = weekly_qc_data.groupby(qc_col)['count'].sum().reset_index()
            weekly_qc_counts.columns = ['QC Name', 'Count']
            
            for i, qc_row in weekly_qc_counts.iterrows():
                worksheet.cell(row=weekly_qc_start+1+i, column=qc_start_col+3, value=str(qc_row['QC Name']))
                worksheet.cell(row=weekly_qc_start+1+i, column=qc_start_col+4, value=int(qc_row['Count']))
        
        # Monthly QC data
        monthly_qc_start = weekly_qc_start + 15
        full_data_copy['Month'] = full_data_copy['Audit Date'].dt.to_period('M')
        monthly_qc_data = full_data_copy.groupby('Month')[qc_col].apply(lambda x: x.dropna().astype(str).value_counts()).reset_index()
        
        monthly_qc_counts = pd.DataFrame()
        if len(monthly_qc_data) > 0:
            monthly_qc_counts = monthly_qc_data.groupby(qc_col)['count'].sum().reset_index()
            monthly_qc_counts.columns = ['QC Name', 'Count']
            
            for i, qc_row in monthly_qc_counts.iterrows():
                worksheet.cell(row=monthly_qc_start+1+i, column=qc_start_col+3, value=str(qc_row['QC Name']))
                worksheet.cell(row=monthly_qc_start+1+i, column=qc_start_col+4, value=int(qc_row['Count']))
        
        # Display formulas that reference the hidden data based on dropdown selection
        max_qc_rows = max(len(daily_qc_counts) if len(daily_qc_counts) > 0 else 0, 10)
        
        for i in range(max_qc_rows):
            # QC Name formula
            name_formula = f'=IF({qc_dropdown_cell.coordinate}="Daily",INDEX({get_column_letter(qc_start_col+3)}:{get_column_letter(qc_start_col+3)},{qc_data_start_row+1+i}),IF({qc_dropdown_cell.coordinate}="Weekly",INDEX({get_column_letter(qc_start_col+3)}:{get_column_letter(qc_start_col+3)},{weekly_qc_start+1+i}),INDEX({get_column_letter(qc_start_col+3)}:{get_column_letter(qc_start_col+3)},{monthly_qc_start+1+i})))'
            name_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col, value=name_formula)
            name_cell.font = data_font
            name_cell.border = thin_border
            name_cell.alignment = Alignment(horizontal='left', vertical='center')
            
            # Count formula
            count_formula = f'=IF({qc_dropdown_cell.coordinate}="Daily",INDEX({get_column_letter(qc_start_col+4)}:{get_column_letter(qc_start_col+4)},{qc_data_start_row+1+i}),IF({qc_dropdown_cell.coordinate}="Weekly",INDEX({get_column_letter(qc_start_col+4)}:{get_column_letter(qc_start_col+4)},{weekly_qc_start+1+i}),INDEX({get_column_letter(qc_start_col+4)}:{get_column_letter(qc_start_col+4)},{monthly_qc_start+1+i})))'
            count_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col+1, value=count_formula)
            count_cell.font = data_font
            count_cell.border = thin_border
            count_cell.alignment = center_alignment
        
        # Hide the data rows
        for row_idx in range(qc_data_start_row, monthly_qc_start + 20):
            worksheet.row_dimensions[row_idx].hidden = True
            
    else:
        no_col_cell = worksheet.cell(row=qc_start_row+3, column=qc_start_col, value="QC column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border

    # Set column widths
    worksheet.column_dimensions[get_column_letter(qc_start_col)].width = 15
    worksheet.column_dimensions[get_column_letter(qc_start_col+1)].width = 10