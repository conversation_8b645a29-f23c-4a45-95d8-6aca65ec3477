import pandas as pd
import openpyxl
from datetime import datetime, timedelta
import random

def create_large_test_dataset():
    """Create a large dataset to test dropdown functionality with many rows"""
    
    # Create sample data with many rows (similar to your actual data structure)
    num_rows = 5000  # Test with 5000 rows
    
    agents = ['Indhumathi R', 'Agent 2', 'Agent 3', 'Agent 4', 'Agent 5']
    managers = ['Indhuma<PERSON> P', 'Manager B', 'Manager C', 'Manager D']
    qc_names = ['Pavithra R', 'QC Person 2', 'QC Person 3', 'QC Person 4']
    practices = ['SIZA', 'Practice B', 'Practice C']
    cpts = ['97153', '97154', '97155', '97156']
    
    data = {
        'Scope': ['Therapy ABA'] * num_rows,
        'Processed Date': [(datetime.now() - timedelta(days=random.randint(0, 30))).strftime('%Y-%m-%d') for _ in range(num_rows)],
        'Audit Date': [(datetime.now() - timedelta(days=random.randint(0, 30))).strftime('%Y-%m-%d') for _ in range(num_rows)],
        'Practice': [random.choice(practices) for _ in range(num_rows)],
        'Patient Name': [f'Patient {i+1}' for i in range(num_rows)],
        'DOS': [(datetime.now() - timedelta(days=random.randint(0, 60))).strftime('%Y-%m-%d') for _ in range(num_rows)],
        'CPT': [random.choice(cpts) for _ in range(num_rows)],
        'Audited transaction': [1] * num_rows,
        'Error(Y/N)': [random.choice(['Yes', 'No']) for _ in range(num_rows)],
        'Error field': [random.choice(['-', 'Incorrect CPT', 'Incorrect DOS', 'Incorrect DX']) for _ in range(num_rows)],
        'Error Description': ['-'] * num_rows,
        'Error Category': [random.choice(['-', 'Typographical error', 'Negligence']) for _ in range(num_rows)],
        'Agent': [random.choice(agents) for _ in range(num_rows)],
        'Reporting To': [random.choice(managers) for _ in range(num_rows)],
        'QC Name': [random.choice(qc_names) for _ in range(num_rows)],
        'Error': [random.randint(0, 1) for _ in range(num_rows)],
        "Week's": [f'Week {random.randint(25, 35)}' for _ in range(num_rows)]
    }
    
    df = pd.DataFrame(data)
    
    # Save to Excel
    filename = 'large_test_dataset.xlsx'
    df.to_excel(filename, sheet_name="Raw Data", index=False)
    
    print(f"Created large test dataset: {filename}")
    print(f"Number of rows: {num_rows}")
    print(f"Columns: {list(df.columns)}")
    
    return filename, df

def test_large_dataset_dropdowns():
    """Test dropdown functionality with large dataset"""
    filename, df = create_large_test_dataset()
    
    # Import the function from main.py
    import sys
    sys.path.append('.')
    from main import add_raw_data_dropdowns_to_workbook
    
    try:
        # Open the file and add dropdowns
        workbook = openpyxl.load_workbook(filename)
        print(f"Loaded workbook with {len(df)} rows")
        
        # Add dropdowns using the main function
        add_raw_data_dropdowns_to_workbook(workbook, df)
        
        # Save the file
        output_filename = 'large_test_with_dropdowns.xlsx'
        workbook.save(output_filename)
        workbook.close()
        
        print(f"Successfully created: {output_filename}")
        print("Testing completed - check if the file opens without corruption warnings")
        
        # Verify the file can be opened
        test_workbook = openpyxl.load_workbook(output_filename)
        sheet = test_workbook["Raw Data"]
        print(f"Verification: File has {len(sheet.data_validations.dataValidation)} data validations")
        test_workbook.close()
        
        return True
        
    except Exception as e:
        print(f"Error during testing: {e}")
        return False

if __name__ == "__main__":
    success = test_large_dataset_dropdowns()
    if success:
        print("\n✅ Large dataset test PASSED - no corruption should occur")
    else:
        print("\n❌ Large dataset test FAILED")
