import pandas as pd
import openpyxl
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.utils import get_column_letter
import io

def test_dropdown_functionality():
    """Test the dropdown functionality directly"""
    
    # Create sample data
    sample_data = {
        'Audit Date': ['2024-01-01', '2024-01-02', '2024-01-03'],
        'Scope': ['In Scope', '', ''],
        'Error(Y/N)': ['Yes', '', ''],
        'Error field': ['Incorrect CPT', '', ''],
        'Error Category': ['Typographical error', '', ''],
        'Agent': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'Reporting To': ['Manager A', 'Manager B', 'Manager A'],
        'QC Name': ['QC Person 1', 'QC Person 2', 'QC Person 1']
    }
    
    df = pd.DataFrame(sample_data)
    
    # Create Excel file
    df.to_excel('test_dropdown_fixed_output.xlsx', sheet_name="Raw Data", index=False)
    
    # Reopen as workbook and add dropdowns
    workbook = openpyxl.load_workbook('test_dropdown_fixed_output.xlsx')
    add_raw_data_dropdowns_to_workbook_test(workbook, df)
    workbook.save('test_dropdown_fixed_output.xlsx')
    
    print("Test file created: test_dropdown_fixed_output.xlsx")
    print("Please open this file in Excel to verify dropdown functionality")

def add_raw_data_dropdowns_to_workbook_test(workbook, original_df):
    """Test version of the dropdown function"""
    from openpyxl.worksheet.datavalidation import DataValidation
    from openpyxl.utils import get_column_letter
    
    # Get the Raw Data worksheet
    raw_data_sheet = workbook["Raw Data"]
    
    # Define dropdown options for each column
    dropdown_options = {
        'Scope': ['In Scope', 'Out of Scope'],
        'Error(Y/N)': ['Yes', 'No'],
        'Error field': [
            'Incorrect 31 and 32 box details',
            'Incorrect Auth#',
            'Incorrect billed amount',
            'Incorrect billing provider details',
            'Incorrect clubbed the session',
            'Incorrect copay amount',
            'Incorrect CPT',
            'Incorrect DOS',
            'Incorrect DX',
            'Incorrect Excel in path',
            'Incorrect HCFA',
            'Incorrect insurance name',
            'Incorrect Modifier',
            'Incorrect pending status in AR tool',
            'Incorrect pending comments',
            'Incorrect POS',
            'Incorrect Units',
            'Incorrect Until Date',
            'Incorrectly billed session',
            'Incorrectly roundoff',
            'Missed to check in motivity',
            'Missed to check MUE',
            'Missed to check old DOS',
            'Missed to dummy the session',
            'Pending line missed in AR Tool',
            'Referring provider details missed',
            'There is no appointment excel in path',
            'Updates not listed in SOP'
        ],
        'Error Category': [
            'Typographical error',
            'Negligence',
            'Missed to check',
            'Updates Missed'
        ]
    }
    
    # Find column indices for the specified columns
    column_indices = {}
    print(f"DEBUG: Original DataFrame columns: {list(original_df.columns)}")
    
    for col_idx, col_name in enumerate(original_df.columns, 1):
        col_name_clean = str(col_name).strip()
        
        # Check for exact matches and variations
        if col_name_clean in dropdown_options:
            column_indices[col_name_clean] = col_idx
        elif col_name_clean.lower() == 'scope':
            column_indices['Scope'] = col_idx
        elif col_name_clean.lower() in ['error(y/n)', 'error (y/n)', 'erroryn', 'error yn']:
            column_indices['Error(Y/N)'] = col_idx
        elif col_name_clean.lower() in ['error field', 'errorfield']:
            column_indices['Error field'] = col_idx
        elif col_name_clean.lower() in ['error category', 'errorcategory']:
            column_indices['Error Category'] = col_idx
        elif col_name_clean.lower() == 'agent':
            column_indices['Agent'] = col_idx
        elif col_name_clean.lower() in ['reporting to', 'reportingto', 'team lead']:
            column_indices['Reporting To'] = col_idx
        elif col_name_clean.lower() in ['qc name', 'qcname', 'qc']:
            column_indices['QC Name'] = col_idx
    
    print(f"DEBUG: Found column indices: {column_indices}")
    
    # Apply dropdowns to each found column
    for col_name, col_idx in column_indices.items():
        if col_name in dropdown_options:
            options = dropdown_options[col_name]
            print(f"DEBUG: Adding dropdown for {col_name} in column {col_idx} with {len(options)} options")
            
            # Create dropdown validation
            if len(options) <= 255:  # Excel dropdown limit
                options_str = ','.join([f'"{opt}"' for opt in options])
                dv = DataValidation(type="list", formula1=options_str, allow_blank=True)
                print(f"DEBUG: Using direct formula for {col_name}")
            else:
                # For longer lists, create a range reference
                options_start_row = len(original_df) + 10
                col_letter = get_column_letter(col_idx + 10)  # Use columns to the right
                
                for i, option in enumerate(options):
                    raw_data_sheet.cell(row=options_start_row + i, column=col_idx + 10, value=option)
                
                options_range = f'{col_letter}{options_start_row}:{col_letter}{options_start_row + len(options) - 1}'
                dv = DataValidation(type="list", formula1=options_range, allow_blank=True)
                print(f"DEBUG: Using range reference for {col_name}: {options_range}")
                
                # Hide the options column
                raw_data_sheet.column_dimensions[col_letter].hidden = True
            
            dv.error = f'Please select a valid {col_name} option'
            dv.errorTitle = f'Invalid {col_name}'
            
            # Apply to all data rows (excluding header)
            col_letter = get_column_letter(col_idx)
            data_range = f'{col_letter}2:{col_letter}{len(original_df) + 1}'
            print(f"DEBUG: Applying dropdown to range {data_range}")
            dv.ranges.add(data_range)
            
            raw_data_sheet.add_data_validation(dv)
            print(f"DEBUG: Successfully added dropdown for {col_name}")
    
    # For Agent, Reporting To, and QC Name columns, create dropdowns from existing data
    dynamic_columns = ['Agent', 'Reporting To', 'QC Name']
    for col_name in dynamic_columns:
        if col_name in column_indices:
            col_idx = column_indices[col_name]
            
            # Get unique values from the column (excluding empty/null values)
            unique_values = original_df.iloc[:, col_idx-1].dropna().astype(str)
            unique_values = unique_values[unique_values.str.strip() != '']
            unique_values = unique_values[unique_values != 'nan']
            unique_values = sorted(unique_values.unique())
            
            if unique_values:
                if len(unique_values) <= 255:  # Excel dropdown limit
                    options_str = ','.join([f'"{val}"' for val in unique_values])
                    dv = DataValidation(type="list", formula1=options_str, allow_blank=True)
                else:
                    # For longer lists, create a range reference
                    options_start_row = len(original_df) + 50
                    col_letter = get_column_letter(col_idx + 15)  # Use columns further to the right
                    
                    for i, value in enumerate(unique_values):
                        raw_data_sheet.cell(row=options_start_row + i, column=col_idx + 15, value=value)
                    
                    options_range = f'{col_letter}{options_start_row}:{col_letter}{options_start_row + len(unique_values) - 1}'
                    dv = DataValidation(type="list", formula1=options_range, allow_blank=True)
                    
                    # Hide the options column
                    raw_data_sheet.column_dimensions[col_letter].hidden = True
                
                dv.error = f'Please select a valid {col_name}'
                dv.errorTitle = f'Invalid {col_name}'
                
                # Apply to all data rows (excluding header)
                col_letter = get_column_letter(col_idx)
                data_range = f'{col_letter}2:{col_letter}{len(original_df) + 1}'
                dv.ranges.add(data_range)
                
                raw_data_sheet.add_data_validation(dv)
                print(f"DEBUG: Added dynamic dropdown for {col_name} with options: {unique_values}")

if __name__ == "__main__":
    test_dropdown_functionality()
