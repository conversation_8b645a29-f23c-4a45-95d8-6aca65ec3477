<!DOCTYPE html>
<html>
<head>
    <title>Processing Large File</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Processing Large File</h2>
        
        <div class="alert alert-info">
            <strong>File:</strong> {{ filename }}<br>
            <strong>Total Rows:</strong> {{ total_rows }}<br>
            <strong>Status:</strong> Processing in background...
        </div>
        
        <div class="progress mb-3">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
        </div>
        
        <div class="text-center">
            <button id="processBtn" class="btn btn-primary" onclick="processFile()">Start Processing</button>
            <div id="loading" class="d-none">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Processing large file... This may take a few minutes.</p>
            </div>
        </div>
        
        <div id="result" class="mt-4 d-none">
            <div class="alert alert-success">
                <strong>Processing Complete!</strong> Your file is ready for download.
            </div>
            <a id="downloadLink" href="#" class="btn btn-success">Download Processed File</a>
        </div>
    </div>
    
    <script>
        async function processFile() {
            document.getElementById('processBtn').classList.add('d-none');
            document.getElementById('loading').classList.remove('d-none');
            
            try {
                const response = await fetch('/process_large_file/{{ session_id }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    // If response is a file download
                    if (response.headers.get('content-type').includes('spreadsheet')) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'processed_{{ filename }}';
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        
                        document.getElementById('loading').classList.add('d-none');
                        document.getElementById('result').classList.remove('d-none');
                    }
                } else {
                    throw new Error('Processing failed');
                }
            } catch (error) {
                document.getElementById('loading').classList.add('d-none');
                alert('Error processing file: ' + error.message);
                document.getElementById('processBtn').classList.remove('d-none');
            }
        }
    </script>
</body>
</html>