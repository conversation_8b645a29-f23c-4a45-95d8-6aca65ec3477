<!DOCTYPE html>
<html>
<head>
    <title>Processing Complete - Audit Analysis</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 700px; margin: 0 auto; padding: 40px 20px; }
        .card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 40px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; font-size: 2.2em; font-weight: 300; }
        .success-icon { text-align: center; font-size: 4em; margin-bottom: 20px; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .info-card { background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%); padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #667eea; }
        .info-card h3 { color: #333; font-size: 1.1em; margin-bottom: 10px; }
        .info-card p { color: #666; font-size: 1.3em; font-weight: 600; }
        .file-info { background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); padding: 25px; border-radius: 10px; margin: 30px 0; border-left: 4px solid #28a745; }
        .file-info h3 { color: #155724; margin-bottom: 15px; font-size: 1.2em; }
        .file-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
        .file-detail { text-align: center; }
        .file-detail .label { color: #6c757d; font-size: 0.9em; margin-bottom: 5px; }
        .file-detail .value { color: #155724; font-weight: 600; font-size: 1.1em; }
        .actions { display: flex; gap: 15px; margin-top: 30px; flex-wrap: wrap; }
        .btn { padding: 15px 30px; border: none; border-radius: 25px; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 10px; font-size: 1.1em; font-weight: 500; transition: all 0.3s ease; flex: 1; justify-content: center; min-width: 200px; }
        .btn-primary { background: linear-gradient(45deg, #28a745, #20c997); color: white; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3); }
        .btn-secondary { background: linear-gradient(45deg, #6c757d, #495057); color: white; }
        .btn-secondary:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3); }
        .features-generated { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 20px; margin: 20px 0; }
        .features-generated h4 { color: #856404; margin-bottom: 15px; }
        .features-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 10px; }
        .feature-item { color: #856404; padding: 8px; background: white; border-radius: 5px; text-align: center; font-size: 0.9em; }
        @media (max-width: 600px) {
            .actions { flex-direction: column; }
            .btn { min-width: auto; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="success-icon">✅</div>
            <h1>Processing Complete!</h1>
            
            <div class="file-info">
                <h3>📄 {{ data.filename }}</h3>
                <div class="file-details">
                    <div class="file-detail">
                        <div class="label">Latest Audit Date</div>
                        <div class="value">{{ data.latest_audit_date }}</div>
                    </div>
                    <div class="file-detail">
                        <div class="label">Total Records</div>
                        <div class="value">{{ data.total_original_rows }}</div>
                    </div>
                    <div class="file-detail">
                        <div class="label">Processed Records</div>
                        <div class="value">{{ data.filtered_rows }}</div>
                    </div>
                </div>
            </div>
            
            <div class="features-generated">
                <h4>📈 Generated Reports & Analysis</h4>
                <div class="features-list">
                    <div class="feature-item">📅 Daily Summary</div>
                    <div class="feature-item">📆 Weekly Summary</div>
                    <div class="feature-item">📇 Monthly Summary</div>
                    <div class="feature-item">❌ Error Analysis</div>
                    <div class="feature-item">📋 QC Summary</div>
                    <div class="feature-item">🎯 Scope Summary</div>
                    <div class="feature-item">📉 Interactive Charts</div>
                    <div class="feature-item">📊 Dynamic Dropdowns</div>
                </div>
            </div>
            
            <div class="info-grid">
                <div class="info-card">
                    <h3>📈 Sheets Generated</h3>
                    <p>5+ Sheets</p>
                </div>
                <div class="info-card">
                    <h3>⏱️ Processing Time</h3>
                    <p>Optimized</p>
                </div>
                <div class="info-card">
                    <h3>📊 Data Analysis</h3>
                    <p>Complete</p>
                </div>
            </div>
            
            <div class="actions">
                <a href="/download/{{ data.file_id }}" class="btn btn-primary">
                    📥 Download Excel Report
                </a>
                <a href="/" class="btn btn-secondary">
                    🔄 Process Another File
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-download after 3 seconds
        setTimeout(() => {
            const downloadBtn = document.querySelector('.btn-primary');
            if (downloadBtn && !sessionStorage.getItem('downloaded')) {
                sessionStorage.setItem('downloaded', 'true');
                // Optional: Uncomment to auto-download
                // window.location.href = downloadBtn.href;
            }
        }, 3000);
    </script>
</body>
</html>