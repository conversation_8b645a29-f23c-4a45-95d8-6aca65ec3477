from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Request, Form
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import pandas as pd
import io
from datetime import datetime
import openpyxl
from openpyxl.utils import get_column_letter
import numpy as np

app = FastAPI()

# Setup templates
templates = Jinja2Templates(directory="templates")

processed_files = {}

@app.get("/", response_class=HTMLResponse)
async def upload_form(request: Request):
    return templates.TemplateResponse("upload.html", {"request": request})

@app.post("/upload")
async def upload_excel(request: Request, file: UploadFile = File(...)):
    if not file.filename.endswith(('.xlsx', '.xls')):
        return templates.TemplateResponse("upload.html", {
            "request": request, 
            "error": "Please upload an Excel file"
        })
    
    contents = await file.read()
    workbook = openpyxl.load_workbook(io.BytesIO(contents), data_only=False)
    worksheet = workbook.active
    
    data = []
    for row in worksheet.iter_rows():
        row_data = []
        for cell in row:
            if cell.value is None:
                row_data.append('')
            elif hasattr(cell, 'displayed_value'):
                row_data.append(str(cell.displayed_value))
            else:
                if cell.number_format and 'd' in cell.number_format.lower():
                    row_data.append(str(cell.value).split(' ')[0] if ' ' in str(cell.value) else str(cell.value))
                else:
                    row_data.append(str(cell.value))
        data.append(row_data)
    
    if not data:
        return templates.TemplateResponse("upload.html", {
            "request": request, 
            "error": "Excel file is empty"
        })
    
    headers = data[0]
    df_data = data[1:]
    df = pd.DataFrame(df_data, columns=headers)
    
    audit_col = None
    for col in df.columns:
        if col.lower().replace(' ', '').replace('_', '') == 'auditdate':
            audit_col = col
            break
    
    if audit_col is None:
        return templates.TemplateResponse("upload.html", {
            "request": request, 
            "error": "Audit Date column not found in the Excel file"
        })
    
    temp_dates = pd.to_datetime(df[audit_col], errors='coerce')
    latest_date = temp_dates.max()
    
    if pd.isna(latest_date):
        return templates.TemplateResponse("upload.html", {
            "request": request, 
            "error": "No valid dates found in Audit Date column"
        })
    
    mask = temp_dates == latest_date
    filtered_df = df[mask].copy()
    
    latest_weekday = latest_date.weekday()
    days_since_sunday = (latest_weekday + 1) % 7
    week_start = latest_date - pd.Timedelta(days=days_since_sunday)
    week_end = week_start + pd.Timedelta(days=6)
    week_mask = (temp_dates >= week_start) & (temp_dates <= week_end)
    weekly_df = df[week_mask].copy()
    
    latest_month_start = latest_date.replace(day=1)
    if latest_date.month == 12:
        latest_month_end = latest_date.replace(year=latest_date.year + 1, month=1, day=1) - pd.Timedelta(days=1)
    else:
        latest_month_end = latest_date.replace(month=latest_date.month + 1, day=1) - pd.Timedelta(days=1)
    month_mask = (temp_dates >= latest_month_start) & (temp_dates <= latest_month_end)
    monthly_df = df[month_mask].copy()
    
    # Pre-compute error data
    error_col = None
    error_df = pd.DataFrame()
    for col in df.columns:
        if col.lower().replace(' ', '').replace('_', '').replace('(', '').replace(')', '') in ['errory/n', 'erroryn', 'error']:
            error_col = col
            error_mask = df[error_col].astype(str).str.lower().str.strip() == 'yes'
            error_df = df[error_mask].copy()
            break
    
    file_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
    
    processed_files[file_id] = {
        'original': df,
        'filtered': filtered_df,
        'weekly': weekly_df,
        'monthly': monthly_df,
        'error': error_df,
        'latest_date': latest_date,
        'audit_col': audit_col,
        'error_col': error_col
    }
    
    # Process data for display
    processed_data = {
        "filename": file.filename,
        "file_id": file_id,
        "latest_audit_date": latest_date.strftime("%Y-%m-%d"),
        "audit_column_found": audit_col,
        "total_original_rows": len(df),
        "filtered_rows": len(filtered_df),
        "columns": list(df.columns)
    }
    
    return templates.TemplateResponse("results.html", {
        "request": request,
        "data": processed_data
    })


@app.get("/download/{file_id}")
async def download_processed_file(file_id: str):
    if file_id not in processed_files:
        return {"error": "File not found"}
    
    # Get pre-computed data
    data = processed_files[file_id]
    filtered_df = data['filtered']
    weekly_df = data['weekly']
    monthly_df = data['monthly']
    error_df = data['error']
    original_df = data['original']
    latest_date = data['latest_date']
    error_col = data['error_col']
    
    # Prepare data for weekly/monthly/daily analysis
    analysis_df = original_df.copy()
    
    # Find Audited transaction column
    audited_transaction_col = None
    for col in original_df.columns:
        col_lower = col.lower().replace(' ', '').replace('_', '')
        if 'audited' in col_lower and 'transaction' in col_lower:
            audited_transaction_col = col
            break
        elif col_lower == 'auditedtransaction':
            audited_transaction_col = col
            break
    
    # Find Error count column (different from Error(Y/N) column)
    error_count_col = None
    for col in original_df.columns:
        if col.lower().strip() == 'error' and col != error_col:
            error_count_col = col
            break
    
    # Add required columns for analysis
    if audited_transaction_col:
        analysis_df['Audited transaction'] = pd.to_numeric(analysis_df[audited_transaction_col], errors='coerce').fillna(1)
    else:
        analysis_df['Audited transaction'] = 1
    
    # Calculate error count based on Error(Y/N) and error count column
    if error_count_col and error_col:
        analysis_df['Has_Error'] = pd.to_numeric(analysis_df[error_count_col], errors='coerce').fillna(0) * (analysis_df[error_col].astype(str).str.lower().str.strip().apply(lambda x: 1 if x == 'yes' else 0))
    elif error_col:
        analysis_df['Has_Error'] = analysis_df[error_col].astype(str).str.lower().str.strip().apply(lambda x: 1 if x == 'yes' else 0)
    else:
        analysis_df['Has_Error'] = 0
    
    # Find Sampling column
    sampling_col = None
    for col in original_df.columns:
        if 'sampling' in col.lower():
            sampling_col = col
            break
    
    analysis_df['Sampling_Count'] = analysis_df[sampling_col].astype(str).str.lower().str.strip().apply(lambda x: 1 if x == 'yes' else 0) if sampling_col else 0
    
    # Find Agent and Team Lead columns
    agent_col = None
    team_lead_col = None
    
    for col in original_df.columns:
        if col.lower().strip() == 'agent':
            agent_col = col
            break
    
    for col in original_df.columns:
        if 'reporting' in col.lower() or 'team' in col.lower() or 'lead' in col.lower():
            team_lead_col = col
            break
    
    if agent_col:
        analysis_df['Agent'] = analysis_df[agent_col]
        analysis_df['Reporting To'] = analysis_df[team_lead_col] if team_lead_col else 'N/A'
        
        # Convert audit date for analysis
        audit_col_name = data['audit_col']
        analysis_df['Audit Date'] = pd.to_datetime(analysis_df[audit_col_name], errors='coerce')
        
        # Prepare weekly, monthly and daily data
        weekly_data, max_week = prepare_weekly_data_dynamic(analysis_df)
        monthly_data, months = prepare_monthly_data(analysis_df)
        daily_data, unique_dates = prepare_daily_data(analysis_df)
    
    # Create Error Analysis data (existing logic)
    error_analysis_df = pd.DataFrame()
    if agent_col:
        current_error_col = error_col
        if current_error_col:
            agent_stats = []
            agent_groups = {}
            
            for agent_name in original_df[agent_col].unique():
                if str(agent_name).strip() and str(agent_name) != 'nan' and str(agent_name) != '':
                    normalized_name = str(agent_name).strip().lower()
                    
                    if normalized_name not in agent_groups:
                        agent_groups[normalized_name] = {
                            'display_name': str(agent_name).strip(),
                            'data': []
                        }
                    
                    agent_data = original_df[original_df[agent_col].astype(str).str.lower().str.strip() == normalized_name]
                    agent_groups[normalized_name]['data'].append(agent_data)
            
            for normalized_name, group_info in agent_groups.items():
                combined_data = pd.concat(group_info['data'], ignore_index=True)
                audited_lines = len(combined_data)
                
                error_count = len(combined_data[combined_data[current_error_col].astype(str).str.lower().str.strip() == 'yes'])
                
                if audited_lines > 0:
                    accuracy_pct = round((1 - (error_count / audited_lines)) * 100, 2)
                    error_pct = round(100 - accuracy_pct, 2)
                else:
                    accuracy_pct = 0
                    error_pct = 0
                
                agent_stats.append({
                    'Agent Name': group_info['display_name'],
                    'Audited Lines': audited_lines,
                    'Error': error_count,
                    'Accuracy%': accuracy_pct,
                    'Error %': error_pct
                })
            
            if agent_stats:
                error_analysis_df = pd.DataFrame(agent_stats)
    
    # Create Excel file
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        daily_sheet_name = f"{latest_date.strftime('%B-%Y')}-Daily"
        weekly_sheet_name = f"{latest_date.strftime('%B-%Y')}-Weekly"
        monthly_sheet_name = f"{latest_date.strftime('%B-%Y')}-Monthly"
        therapy_header = f"Therapy_{latest_date.strftime('%B-%Y')}"
        
        # Write enhanced sheets with Daily first if agent data available
        if agent_col and not weekly_data.empty:
            create_daily_summary_sheet_simple(writer, daily_data, unique_dates, daily_sheet_name, analysis_df)
            create_weekly_summary_sheet_simple(writer, weekly_data, max_week, weekly_sheet_name, analysis_df)
            create_monthly_summary_sheet_simple(writer, monthly_data, months, monthly_sheet_name, analysis_df)
        else:
            # Fallback to basic sheets
            weekly_df.to_excel(writer, sheet_name=weekly_sheet_name, index=False)
            monthly_df.to_excel(writer, sheet_name=monthly_sheet_name, index=False)
        
        if not error_df.empty:
            create_error_details_sheet_with_chart(writer, error_df, original_df, error_col, "Error Details")
        
        if not error_analysis_df.empty:
            total_audited = error_analysis_df['Audited Lines'].sum()
            total_errors = error_analysis_df['Error'].sum()
            total_accuracy = round((1 - (total_errors / total_audited)) * 100, 2) if total_audited > 0 else 0
            total_error_pct = round(100 - total_accuracy, 2)
            
            total_row = pd.DataFrame({
                'Agent Name': ['Total'],
                'Audited Lines': [total_audited],
                'Error': [total_errors],
                'Accuracy%': [total_accuracy],
                'Error %': [total_error_pct]
            })
            
            error_analysis_with_total = pd.concat([error_analysis_df, total_row], ignore_index=True)
            error_analysis_with_total.to_excel(writer, sheet_name="Error Analysis", index=False, startrow=1)
            
            worksheet = writer.sheets["Error Analysis"]
            worksheet.cell(row=1, column=1, value=therapy_header)
        
        original_df.to_excel(writer, sheet_name="Raw Data", index=False)
    
    # Reorder sheets to put Daily first
    workbook = openpyxl.load_workbook(output)
    if daily_sheet_name in workbook.sheetnames:
        daily_sheet = workbook[daily_sheet_name]
        workbook.move_sheet(daily_sheet, offset=-len(workbook.sheetnames)+1)
    
    # Save the reordered workbook
    output.seek(0)
    output.truncate(0)
    workbook.save(output)
    output.seek(0)
    
    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={"Content-Disposition": f"attachment; filename=processed_{file_id}.xlsx"}
    )

def prepare_weekly_data_dynamic(data):
    data['Week_Num'] = ((data['Audit Date'] - data['Audit Date'].min()).dt.days // 7) + 1
    max_week = int(data['Week_Num'].max())
    
    weekly_summary = data.groupby(['Agent', 'Reporting To', 'Week_Num']).agg({
        'Audited transaction': 'sum',
        'Has_Error': 'sum',
        'Sampling_Count': 'sum'
    }).reset_index()
    
    weekly_summary['Score'] = np.where(
        weekly_summary['Audited transaction'] > 0,
        ((weekly_summary['Audited transaction'] - weekly_summary['Has_Error']) / 
         weekly_summary['Audited transaction'] * 100).round(2),
        100.0
    )
    
    agents = data[['Agent', 'Reporting To']].drop_duplicates()
    result_data = []
    
    for _, agent in agents.iterrows():
        row = {'Agent Name': agent['Agent'], 'Team Lead': agent['Reporting To']}
        
        for week in range(1, max_week + 1):
            week_data = weekly_summary[
                (weekly_summary['Agent'] == agent['Agent']) & 
                (weekly_summary['Week_Num'] == week)
            ]
            
            if len(week_data) > 0:
                row[f'Week_{week}_Audited'] = int(week_data['Audited transaction'].iloc[0])
                row[f'Week_{week}_Errors'] = int(week_data['Has_Error'].iloc[0])
                row[f'Week_{week}_Score'] = float(week_data['Score'].iloc[0])
                row[f'Week_{week}_Productivity'] = int(week_data['Sampling_Count'].iloc[0])
            else:
                row[f'Week_{week}_Audited'] = 0
                row[f'Week_{week}_Errors'] = 0
                row[f'Week_{week}_Score'] = 100.00
                row[f'Week_{week}_Productivity'] = 0
        
        result_data.append(row)
    
    total_row = {'Agent Name': 'Total', 'Team Lead': ''}
    for week in range(1, max_week + 1):
        week_total_data = weekly_summary[weekly_summary['Week_Num'] == week]
        total_audited = int(week_total_data['Audited transaction'].sum())
        total_errors = int(week_total_data['Has_Error'].sum())
        total_score = ((total_audited - total_errors) / total_audited * 100) if total_audited > 0 else 100.00
        
        total_productivity = int(week_total_data['Sampling_Count'].sum())
        total_row[f'Week_{week}_Audited'] = total_audited
        total_row[f'Week_{week}_Errors'] = total_errors
        total_row[f'Week_{week}_Score'] = round(total_score, 2)
        total_row[f'Week_{week}_Productivity'] = total_productivity
    
    result_data.append(total_row)
    return pd.DataFrame(result_data), max_week

def prepare_monthly_data(data):
    data = data.copy()
    data['Month_Year'] = data['Audit Date'].dt.to_period('M').astype(str)
    
    monthly = data.groupby(['Agent', 'Reporting To', 'Month_Year']).agg({
        'Audited transaction': 'sum',
        'Has_Error': 'sum',
        'Sampling_Count': 'sum'
    }).reset_index()
    
    monthly['Score'] = np.where(
        monthly['Audited transaction'] > 0,
        ((monthly['Audited transaction'] - monthly['Has_Error']) / 
         monthly['Audited transaction'] * 100).round(2),
        100.0
    )
    
    agents = data[['Agent','Reporting To']].drop_duplicates()
    months = sorted(monthly['Month_Year'].unique())
    
    rows = []
    for _, ag in agents.iterrows():
        row = {'Agent Name':ag['Agent'], 'Team Lead':ag['Reporting To']}
        for m in months:
            slice_ = monthly[
                (monthly['Agent']==ag['Agent']) & 
                (monthly['Month_Year']==m)
            ]
            if not slice_.empty:
                row[f'{m}_Audited'] = int(slice_['Audited transaction'].iloc[0])
                row[f'{m}_Errors']  = int(slice_['Has_Error'].iloc[0])
                row[f'{m}_Score']   = float(slice_['Score'].iloc[0])
                row[f'{m}_Productivity'] = int(slice_['Sampling_Count'].iloc[0])
            else:
                row[f'{m}_Audited'] = 0
                row[f'{m}_Errors']  = 0
                row[f'{m}_Score']   = 100.00
                row[f'{m}_Productivity'] = 0
        rows.append(row)
    
    # Add total row
    total_row = {'Agent Name': 'Total', 'Team Lead': ''}
    for m in months:
        month_total_data = monthly[monthly['Month_Year'] == m]
        total_audited = int(month_total_data['Audited transaction'].sum())
        total_errors = int(month_total_data['Has_Error'].sum())
        total_score = ((total_audited - total_errors) / total_audited * 100) if total_audited > 0 else 100.00
        total_productivity = int(month_total_data['Sampling_Count'].sum())
        
        total_row[f'{m}_Audited'] = total_audited
        total_row[f'{m}_Errors'] = total_errors
        total_row[f'{m}_Score'] = round(total_score, 2)
        total_row[f'{m}_Productivity'] = total_productivity
    rows.append(total_row)
    
    return pd.DataFrame(rows), months

def create_weekly_summary_sheet_simple(writer, weekly_data, max_week, sheet_name, data):
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    import pandas as pd
    
    # Get the workbook and create worksheet
    workbook = writer.book
    worksheet = workbook.create_sheet(title=sheet_name)
    
    # Define styles
    title_font = Font(bold=True, size=14)
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    total_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")
    
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Get date range
    start_date = data['Audit Date'].min().strftime('%m/%d/%Y')
    end_date = data['Audit Date'].max().strftime('%m/%d/%Y')
    
    # Title row
    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=6)
    title_cell = worksheet.cell(row=1, column=1, value=f'Weekly Audit_Charge({start_date} - {end_date})')
    title_cell.font = title_font
    title_cell.fill = title_fill
    title_cell.alignment = center_alignment
    title_cell.border = thin_border
    
    # Week selector dropdown
    worksheet.cell(row=2, column=1, value='Select Week:')
    dropdown_cell = worksheet.cell(row=2, column=2, value=max_week)  # Default to latest week
    dropdown_cell.fill = dropdown_fill
    dropdown_cell.border = thin_border
    dropdown_cell.alignment = center_alignment
    
    # Create dropdown validation with numbers only
    week_numbers = ','.join([str(i) for i in range(1, max_week + 1)])
    dv = DataValidation(type="list", formula1=f'"{week_numbers}"', allow_blank=False)
    dv.error = 'Please select a valid week number'
    dv.errorTitle = 'Invalid Week'
    worksheet.add_data_validation(dv)
    dv.add(dropdown_cell)
    
    # Headers
    worksheet.merge_cells(start_row=4, start_column=1, end_row=5, end_column=1)
    agent_header = worksheet.cell(row=4, column=1, value='Agent Name')
    agent_header.font = header_font
    agent_header.fill = header_fill
    agent_header.alignment = center_alignment
    agent_header.border = thin_border
    
    worksheet.merge_cells(start_row=4, start_column=2, end_row=5, end_column=2)
    lead_header = worksheet.cell(row=4, column=2, value='Team Lead')
    lead_header.font = header_font
    lead_header.fill = header_fill
    lead_header.alignment = center_alignment
    lead_header.border = thin_border
    
    # Create hidden data area
    data_start_row = len(weekly_data) + 10
    
    # Calculate week date ranges and store in hidden area
    week_ranges_start_row = data_start_row + max_week * (len(weekly_data) + 2) + 5
    min_date = data['Audit Date'].min()
    
    for week in range(1, max_week + 1):
        week_start = min_date + pd.Timedelta(days=(week - 1) * 7)
        week_end = week_start + pd.Timedelta(days=6)
        worksheet.cell(row=week_ranges_start_row + week - 1, column=13, value=week)
        worksheet.cell(row=week_ranges_start_row + week - 1, column=14, value=week_start.strftime('%m/%d/%Y'))
        worksheet.cell(row=week_ranges_start_row + week - 1, column=15, value=week_end.strftime('%m/%d/%Y'))
    
    # Dynamic week header with date range
    worksheet.merge_cells(start_row=4, start_column=3, end_row=4, end_column=6)
    week_header = worksheet.cell(row=4, column=3, value=f'="Week " & B2 & " (" & INDEX(N:N, {week_ranges_start_row} + B2 - 1) & " - " & INDEX(O:O, {week_ranges_start_row} + B2 - 1) & ")"')
    week_header.font = header_font
    week_header.fill = header_fill
    week_header.alignment = center_alignment
    week_header.border = thin_border
    
    # Sub-headers
    for i, sub_header in enumerate(['Audited Claims', 'Error', 'Score', 'Productivity']):
        sub_cell = worksheet.cell(row=5, column=3 + i, value=sub_header)
        sub_cell.font = header_font
        sub_cell.fill = header_fill
        sub_cell.alignment = center_alignment
        sub_cell.border = thin_border
    
    # Write all weekly data in hidden area
    for week in range(1, max_week + 1):
        week_start_row = data_start_row + (week - 1) * (len(weekly_data) + 2)
        
        # Week header in hidden area
        worksheet.cell(row=week_start_row, column=7, value=f'Week_{week}')
        
        for idx, (_, agent_data) in enumerate(weekly_data.iterrows()):
            row_num = week_start_row + idx + 1
            
            # Agent name
            worksheet.cell(row=row_num, column=7, value=agent_data['Agent Name'])
            
            # Team lead
            worksheet.cell(row=row_num, column=8, value=agent_data['Team Lead'])
            
            # Week data
            audited = agent_data[f'Week_{week}_Audited']
            errors = agent_data[f'Week_{week}_Errors']
            score = agent_data[f'Week_{week}_Score']
            productivity = agent_data[f'Week_{week}_Productivity']
            
            worksheet.cell(row=row_num, column=9, value=audited)
            worksheet.cell(row=row_num, column=10, value=errors)
            worksheet.cell(row=row_num, column=11, value=score)
            worksheet.cell(row=row_num, column=12, value=productivity)
    
    # Data rows with dynamic formulas
    row_num = 6
    for idx, (_, agent_data) in enumerate(weekly_data.iterrows()):
        # Agent name
        agent_cell = worksheet.cell(row=row_num, column=1, value=agent_data['Agent Name'])
        agent_cell.font = data_font
        agent_cell.alignment = Alignment(horizontal='left', vertical='center')
        agent_cell.border = thin_border
        
        # Team lead
        lead_cell = worksheet.cell(row=row_num, column=2, value=agent_data['Team Lead'])
        lead_cell.font = data_font
        lead_cell.alignment = center_alignment
        lead_cell.border = thin_border
        
        # Check if this is the total row
        is_total = agent_data['Agent Name'] == 'Total'
        
        # Dynamic formulas that lookup data based on selected week
        base_row = data_start_row + 1 + idx
        
        # Audited claims
        audited_formula = f'=INDEX(I{base_row}:I{base_row + max_week * (len(weekly_data) + 2)}, ($B$2-1)*{len(weekly_data) + 2}+1)'
        audited_cell = worksheet.cell(row=row_num, column=3, value=audited_formula)
        audited_cell.font = Font(bold=is_total, size=10)
        audited_cell.alignment = center_alignment
        audited_cell.border = thin_border
        if is_total:
            audited_cell.fill = total_fill
        
        # Errors
        error_formula = f'=INDEX(J{base_row}:J{base_row + max_week * (len(weekly_data) + 2)}, ($B$2-1)*{len(weekly_data) + 2}+1)'
        error_cell = worksheet.cell(row=row_num, column=4, value=error_formula)
        error_cell.font = Font(bold=is_total, size=10)
        error_cell.alignment = center_alignment
        error_cell.border = thin_border
        if is_total:
            error_cell.fill = total_fill
        
        # Score
        score_formula = f'=INDEX(K{base_row}:K{base_row + max_week * (len(weekly_data) + 2)}, ($B$2-1)*{len(weekly_data) + 2}+1)/100'
        score_cell = worksheet.cell(row=row_num, column=5, value=score_formula)
        score_cell.font = Font(bold=is_total, size=10)
        score_cell.alignment = center_alignment
        score_cell.border = thin_border
        score_cell.number_format = '0.00%'
        if is_total:
            score_cell.fill = total_fill
        
        # Productivity
        productivity_formula = f'=INDEX(L{base_row}:L{base_row + max_week * (len(weekly_data) + 2)}, ($B$2-1)*{len(weekly_data) + 2}+1)'
        productivity_cell = worksheet.cell(row=row_num, column=6, value=productivity_formula)
        productivity_cell.font = Font(bold=is_total, size=10)
        productivity_cell.alignment = center_alignment
        productivity_cell.border = thin_border
        if is_total:
            productivity_cell.fill = total_fill
        
        row_num += 1
    
    # Hide the data rows used for formulas
    for week in range(1, max_week + 1):
        week_start_row = data_start_row + (week - 1) * (len(weekly_data) + 2)
        for row_idx in range(week_start_row, week_start_row + len(weekly_data) + 2):
            worksheet.row_dimensions[row_idx].hidden = True
    
    # Set column widths
    worksheet.column_dimensions['A'].width = 20
    worksheet.column_dimensions['B'].width = 12
    worksheet.column_dimensions['C'].width = 15
    worksheet.column_dimensions['D'].width = 10
    worksheet.column_dimensions['E'].width = 12
    worksheet.column_dimensions['F'].width = 12
    
    # Hide the data columns
    worksheet.column_dimensions['G'].hidden = True
    worksheet.column_dimensions['H'].hidden = True
    worksheet.column_dimensions['I'].hidden = True
    worksheet.column_dimensions['J'].hidden = True
    worksheet.column_dimensions['K'].hidden = True
    worksheet.column_dimensions['L'].hidden = True
    worksheet.column_dimensions['M'].hidden = True
    worksheet.column_dimensions['N'].hidden = True
    worksheet.column_dimensions['O'].hidden = True

def create_monthly_summary_sheet_simple(writer, monthly_data, months, sheet_name, data):
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    
    # Get the workbook and create worksheet
    workbook = writer.book
    worksheet = workbook.create_sheet(title=sheet_name)
    
    # Define styles
    title_font = Font(bold=True, size=14)
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    total_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")
    
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Get date range
    start_date = data['Audit Date'].min().strftime('%m/%d/%Y')
    end_date = data['Audit Date'].max().strftime('%m/%d/%Y')
    
    # Title row
    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=6)
    title_cell = worksheet.cell(row=1, column=1, value=f'Monthly Audit_Charge({start_date} - {end_date})')
    title_cell.font = title_font
    title_cell.fill = title_fill
    title_cell.alignment = center_alignment
    title_cell.border = thin_border
    
    # Month selector dropdown
    worksheet.cell(row=2, column=1, value='Select Month:')
    dropdown_cell = worksheet.cell(row=2, column=2, value=months[-1])  # Default to latest month
    dropdown_cell.fill = dropdown_fill
    dropdown_cell.border = thin_border
    dropdown_cell.alignment = center_alignment
    
    # Create dropdown validation
    month_list = ','.join(months)
    dv = DataValidation(type="list", formula1=f'"{month_list}"', allow_blank=False)
    dv.error = 'Please select a valid month'
    dv.errorTitle = 'Invalid Month'
    worksheet.add_data_validation(dv)
    dv.add(dropdown_cell)
    
    # Headers
    worksheet.merge_cells(start_row=4, start_column=1, end_row=5, end_column=1)
    agent_header = worksheet.cell(row=4, column=1, value='Agent Name')
    agent_header.font = header_font
    agent_header.fill = header_fill
    agent_header.alignment = center_alignment
    agent_header.border = thin_border
    
    worksheet.merge_cells(start_row=4, start_column=2, end_row=5, end_column=2)
    lead_header = worksheet.cell(row=4, column=2, value='Team Lead')
    lead_header.font = header_font
    lead_header.fill = header_fill
    lead_header.alignment = center_alignment
    lead_header.border = thin_border
    
    # Dynamic month header
    worksheet.merge_cells(start_row=4, start_column=3, end_row=4, end_column=6)
    month_header = worksheet.cell(row=4, column=3, value='=B2')
    month_header.font = header_font
    month_header.fill = header_fill
    month_header.alignment = center_alignment
    month_header.border = thin_border
    
    # Sub-headers
    for i, sub_header in enumerate(['Audited Claims', 'Error', 'Score', 'Productivity']):
        sub_cell = worksheet.cell(row=5, column=3 + i, value=sub_header)
        sub_cell.font = header_font
        sub_cell.fill = header_fill
        sub_cell.alignment = center_alignment
        sub_cell.border = thin_border
    
    # Create hidden data area
    data_start_row = len(monthly_data) + 10
    
    # Write all monthly data in hidden area
    for month_idx, month in enumerate(months):
        month_start_row = data_start_row + month_idx * (len(monthly_data) + 2)
        
        # Month header in hidden area
        worksheet.cell(row=month_start_row, column=7, value=month)
        
        for idx, (_, agent_data) in enumerate(monthly_data.iterrows()):
            row_num = month_start_row + idx + 1
            
            # Agent name
            worksheet.cell(row=row_num, column=7, value=agent_data['Agent Name'])
            
            # Team lead
            worksheet.cell(row=row_num, column=8, value=agent_data['Team Lead'])
            
            # Month data
            audited = agent_data[f'{month}_Audited']
            errors = agent_data[f'{month}_Errors']
            score = agent_data[f'{month}_Score']
            productivity = agent_data[f'{month}_Productivity']
            
            worksheet.cell(row=row_num, column=9, value=audited)
            worksheet.cell(row=row_num, column=10, value=errors)
            worksheet.cell(row=row_num, column=11, value=score)
            worksheet.cell(row=row_num, column=12, value=productivity)
    
    # Data rows with dynamic formulas
    row_num = 6
    for idx, (_, agent_data) in enumerate(monthly_data.iterrows()):
        # Agent name
        agent_cell = worksheet.cell(row=row_num, column=1, value=agent_data['Agent Name'])
        agent_cell.font = data_font
        agent_cell.alignment = Alignment(horizontal='left', vertical='center')
        agent_cell.border = thin_border
        
        # Team lead
        lead_cell = worksheet.cell(row=row_num, column=2, value=agent_data['Team Lead'])
        lead_cell.font = data_font
        lead_cell.alignment = center_alignment
        lead_cell.border = thin_border
        
        # Check if this is the total row
        is_total = agent_data['Agent Name'] == 'Total'
        
        # Dynamic formulas that lookup data based on selected month
        base_row = data_start_row + 1 + idx
        
        # Audited claims
        audited_formula = f'=INDEX(I{base_row}:I{base_row + len(months) * (len(monthly_data) + 2)}, MATCH(B2, G{data_start_row}:G{data_start_row + len(months) * (len(monthly_data) + 2)}, 0) + {idx})'
        audited_cell = worksheet.cell(row=row_num, column=3, value=audited_formula)
        audited_cell.font = Font(bold=is_total, size=10)
        audited_cell.alignment = center_alignment
        audited_cell.border = thin_border
        if is_total:
            audited_cell.fill = total_fill
        
        # Errors
        error_formula = f'=INDEX(J{base_row}:J{base_row + len(months) * (len(monthly_data) + 2)}, MATCH(B2, G{data_start_row}:G{data_start_row + len(months) * (len(monthly_data) + 2)}, 0) + {idx})'
        error_cell = worksheet.cell(row=row_num, column=4, value=error_formula)
        error_cell.font = Font(bold=is_total, size=10)
        error_cell.alignment = center_alignment
        error_cell.border = thin_border
        if is_total:
            error_cell.fill = total_fill
        
        # Score
        score_formula = f'=INDEX(K{base_row}:K{base_row + len(months) * (len(monthly_data) + 2)}, MATCH(B2, G{data_start_row}:G{data_start_row + len(months) * (len(monthly_data) + 2)}, 0) + {idx})/100'
        score_cell = worksheet.cell(row=row_num, column=5, value=score_formula)
        score_cell.font = Font(bold=is_total, size=10)
        score_cell.alignment = center_alignment
        score_cell.border = thin_border
        score_cell.number_format = '0.00%'
        if is_total:
            score_cell.fill = total_fill
        
        # Productivity
        productivity_formula = f'=INDEX(L{base_row}:L{base_row + len(months) * (len(monthly_data) + 2)}, MATCH(B2, G{data_start_row}:G{data_start_row + len(months) * (len(monthly_data) + 2)}, 0) + {idx})'
        productivity_cell = worksheet.cell(row=row_num, column=6, value=productivity_formula)
        productivity_cell.font = Font(bold=is_total, size=10)
        productivity_cell.alignment = center_alignment
        productivity_cell.border = thin_border
        if is_total:
            productivity_cell.fill = total_fill
        
        row_num += 1
    
    # Hide the data rows used for formulas
    for month_idx in range(len(months)):
        month_start_row = data_start_row + month_idx * (len(monthly_data) + 2)
        for row_idx in range(month_start_row, month_start_row + len(monthly_data) + 2):
            worksheet.row_dimensions[row_idx].hidden = True
    
    # Set column widths
    worksheet.column_dimensions['A'].width = 20
    worksheet.column_dimensions['B'].width = 12
    worksheet.column_dimensions['C'].width = 15
    worksheet.column_dimensions['D'].width = 10
    worksheet.column_dimensions['E'].width = 12
    worksheet.column_dimensions['F'].width = 12
    
    # Hide the data columns
    worksheet.column_dimensions['G'].hidden = True
    worksheet.column_dimensions['H'].hidden = True
    worksheet.column_dimensions['I'].hidden = True
    worksheet.column_dimensions['J'].hidden = True
    worksheet.column_dimensions['K'].hidden = True
    worksheet.column_dimensions['L'].hidden = True

def create_daily_summary_sheet_simple(writer, daily_data, unique_dates, sheet_name, data):
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    
    # Get the workbook and create worksheet
    workbook = writer.book
    worksheet = workbook.create_sheet(title=sheet_name)
    
    # Define styles
    title_font = Font(bold=True, size=14)
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    total_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")
    
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Get date range
    start_date = data['Audit Date'].min().strftime('%m/%d/%Y')
    end_date = data['Audit Date'].max().strftime('%m/%d/%Y')
    
    # Title row
    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=6)
    title_cell = worksheet.cell(row=1, column=1, value=f'Daily Audit_Charge({start_date} - {end_date})')
    title_cell.font = title_font
    title_cell.fill = title_fill
    title_cell.alignment = center_alignment
    title_cell.border = thin_border
    
    # Date selector dropdown
    worksheet.cell(row=2, column=1, value='Select Date:')
    dropdown_cell = worksheet.cell(row=2, column=2, value=unique_dates[-1].strftime('%Y-%m-%d'))  # Default to latest date
    dropdown_cell.fill = dropdown_fill
    dropdown_cell.border = thin_border
    dropdown_cell.alignment = center_alignment
    
    # Create dropdown validation with proper range
    date_range_start = len(daily_data) + 20
    for i, date in enumerate(unique_dates):
        worksheet.cell(row=date_range_start + i, column=13, value=date.strftime('%Y-%m-%d'))
    
    date_range = f'M{date_range_start}:M{date_range_start + len(unique_dates) - 1}'
    dv = DataValidation(type="list", formula1=date_range, allow_blank=False)
    dv.error = 'Please select a valid date'
    dv.errorTitle = 'Invalid Date'
    worksheet.add_data_validation(dv)
    dv.add(dropdown_cell)
    
    # Hide the date list column
    worksheet.column_dimensions['M'].hidden = True
    
    # Headers
    worksheet.merge_cells(start_row=4, start_column=1, end_row=5, end_column=1)
    agent_header = worksheet.cell(row=4, column=1, value='Agent Name')
    agent_header.font = header_font
    agent_header.fill = header_fill
    agent_header.alignment = center_alignment
    agent_header.border = thin_border
    
    worksheet.merge_cells(start_row=4, start_column=2, end_row=5, end_column=2)
    lead_header = worksheet.cell(row=4, column=2, value='Team Lead')
    lead_header.font = header_font
    lead_header.fill = header_fill
    lead_header.alignment = center_alignment
    lead_header.border = thin_border
    
    # Dynamic date header
    worksheet.merge_cells(start_row=4, start_column=3, end_row=4, end_column=6)
    date_header = worksheet.cell(row=4, column=3, value='=B2')
    date_header.font = header_font
    date_header.fill = header_fill
    date_header.alignment = center_alignment
    date_header.border = thin_border
    
    # Sub-headers
    for i, sub_header in enumerate(['Audited Claims', 'Error', 'Score', 'Productivity']):
        sub_cell = worksheet.cell(row=5, column=3 + i, value=sub_header)
        sub_cell.font = header_font
        sub_cell.fill = header_fill
        sub_cell.alignment = center_alignment
        sub_cell.border = thin_border
    
    # Create hidden data area
    data_start_row = len(daily_data) + 10
    
    # Write all daily data in hidden area with proper structure
    for date_idx, date in enumerate(unique_dates):
        date_str = date.strftime('%Y-%m-%d')
        date_header_row = data_start_row + date_idx * (len(daily_data) + 1)
        
        # Date header in hidden area
        worksheet.cell(row=date_header_row, column=7, value=date_str)
        
        for idx, (_, agent_data) in enumerate(daily_data.iterrows()):
            data_row = date_header_row + idx + 1
            
            # Daily data for this date and agent
            audited = agent_data[f'{date_str}_Audited']
            errors = agent_data[f'{date_str}_Errors']
            score = agent_data[f'{date_str}_Score']
            productivity = agent_data[f'{date_str}_Productivity']
            
            worksheet.cell(row=data_row, column=9, value=audited)
            worksheet.cell(row=data_row, column=10, value=errors)
            worksheet.cell(row=data_row, column=11, value=score)
            worksheet.cell(row=data_row, column=12, value=productivity)
    
    # Data rows with dynamic formulas
    row_num = 6
    for idx, (_, agent_data) in enumerate(daily_data.iterrows()):
        # Agent name
        agent_cell = worksheet.cell(row=row_num, column=1, value=agent_data['Agent Name'])
        agent_cell.font = data_font
        agent_cell.alignment = Alignment(horizontal='left', vertical='center')
        agent_cell.border = thin_border
        
        # Team lead
        lead_cell = worksheet.cell(row=row_num, column=2, value=agent_data['Team Lead'])
        lead_cell.font = data_font
        lead_cell.alignment = center_alignment
        lead_cell.border = thin_border
        
        # Check if this is the total row
        is_total = agent_data['Agent Name'] == 'Total'
        
        # Audited claims
        audited_formula = f'=IFERROR(INDEX(I:I, MATCH(B2, G:G, 0) + {idx + 1}), 0)'
        audited_cell = worksheet.cell(row=row_num, column=3, value=audited_formula)
        audited_cell.font = Font(bold=is_total, size=10)
        audited_cell.alignment = center_alignment
        audited_cell.border = thin_border
        if is_total:
            audited_cell.fill = total_fill
        
        # Errors
        error_formula = f'=IFERROR(INDEX(J:J, MATCH(B2, G:G, 0) + {idx + 1}), 0)'
        error_cell = worksheet.cell(row=row_num, column=4, value=error_formula)
        error_cell.font = Font(bold=is_total, size=10)
        error_cell.alignment = center_alignment
        error_cell.border = thin_border
        if is_total:
            error_cell.fill = total_fill
        
        # Score
        score_formula = f'=IFERROR(INDEX(K:K, MATCH(B2, G:G, 0) + {idx + 1})/100, 1)'
        score_cell = worksheet.cell(row=row_num, column=5, value=score_formula)
        score_cell.font = Font(bold=is_total, size=10)
        score_cell.alignment = center_alignment
        score_cell.border = thin_border
        score_cell.number_format = '0.00%'
        if is_total:
            score_cell.fill = total_fill
        
        # Productivity
        productivity_formula = f'=IFERROR(INDEX(L:L, MATCH(B2, G:G, 0) + {idx + 1}), 0)'
        productivity_cell = worksheet.cell(row=row_num, column=6, value=productivity_formula)
        productivity_cell.font = Font(bold=is_total, size=10)
        productivity_cell.alignment = center_alignment
        productivity_cell.border = thin_border
        if is_total:
            productivity_cell.fill = total_fill
        
        row_num += 1
    
    # Hide the data rows used for formulas
    for date_idx in range(len(unique_dates)):
        date_header_row = data_start_row + date_idx * (len(daily_data) + 1)
        for row_idx in range(date_header_row, date_header_row + len(daily_data) + 1):
            worksheet.row_dimensions[row_idx].hidden = True
    
    # Set column widths
    worksheet.column_dimensions['A'].width = 20
    worksheet.column_dimensions['B'].width = 12
    worksheet.column_dimensions['C'].width = 15
    worksheet.column_dimensions['D'].width = 10
    worksheet.column_dimensions['E'].width = 12
    worksheet.column_dimensions['F'].width = 12
    
    # Hide the data columns
    worksheet.column_dimensions['G'].hidden = True
    worksheet.column_dimensions['H'].hidden = True
    worksheet.column_dimensions['I'].hidden = True
    worksheet.column_dimensions['J'].hidden = True
    worksheet.column_dimensions['K'].hidden = True
    worksheet.column_dimensions['L'].hidden = True

def prepare_daily_data(data):
    # Get unique dates and sort them
    unique_dates = sorted(data['Audit Date'].dt.date.unique())
    
    daily_summary = data.groupby(['Agent', 'Reporting To', data['Audit Date'].dt.date]).agg({
        'Audited transaction': 'sum',
        'Has_Error': 'sum',
        'Sampling_Count': 'sum'
    }).reset_index()
    
    daily_summary['Score'] = np.where(
        daily_summary['Audited transaction'] > 0,
        ((daily_summary['Audited transaction'] - daily_summary['Has_Error']) / 
         daily_summary['Audited transaction'] * 100).round(2),
        100.0
    )
    
    agents = data[['Agent', 'Reporting To']].drop_duplicates()
    result_data = []
    
    for _, agent in agents.iterrows():
        row = {'Agent Name': agent['Agent'], 'Team Lead': agent['Reporting To']}
        
        for date in unique_dates:
            date_data = daily_summary[
                (daily_summary['Agent'] == agent['Agent']) & 
                (daily_summary['Audit Date'] == date)
            ]
            
            date_str = date.strftime('%Y-%m-%d')
            if len(date_data) > 0:
                row[f'{date_str}_Audited'] = int(date_data['Audited transaction'].iloc[0])
                row[f'{date_str}_Errors'] = int(date_data['Has_Error'].iloc[0])
                row[f'{date_str}_Score'] = float(date_data['Score'].iloc[0])
                row[f'{date_str}_Productivity'] = int(date_data['Sampling_Count'].iloc[0])
            else:
                row[f'{date_str}_Audited'] = 0
                row[f'{date_str}_Errors'] = 0
                row[f'{date_str}_Score'] = 100.00
                row[f'{date_str}_Productivity'] = 0
        
        result_data.append(row)
    
    # Add total row
    total_row = {'Agent Name': 'Total', 'Team Lead': ''}
    for date in unique_dates:
        date_total_data = daily_summary[daily_summary['Audit Date'] == date]
        total_audited = int(date_total_data['Audited transaction'].sum())
        total_errors = int(date_total_data['Has_Error'].sum())
        total_score = ((total_audited - total_errors) / total_audited * 100) if total_audited > 0 else 100.00
        
        date_str = date.strftime('%Y-%m-%d')
        total_productivity = int(date_total_data['Sampling_Count'].sum())
        total_row[f'{date_str}_Audited'] = total_audited
        total_row[f'{date_str}_Errors'] = total_errors
        total_row[f'{date_str}_Score'] = round(total_score, 2)
        total_row[f'{date_str}_Productivity'] = total_productivity
    
    result_data.append(total_row)
    return pd.DataFrame(result_data), unique_dates

def create_error_details_sheet_with_chart(writer, error_df, original_df, error_col, sheet_name):
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    from openpyxl.chart import BarChart, Reference
    
    workbook = writer.book
    worksheet = workbook.create_sheet(title=sheet_name)
    
    # Styles
    title_font = Font(bold=True, size=14)
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Title
    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=6)
    title_cell = worksheet.cell(row=1, column=1, value='Error Field vs Agent Analysis')
    title_cell.font = title_font
    title_cell.fill = title_fill
    title_cell.alignment = center_alignment
    title_cell.border = thin_border
    
    # Error field dropdown
    worksheet.cell(row=2, column=1, value='Select Error:')
    
    # Debug: Print all columns
    print(f"All columns in error_df: {list(error_df.columns)}")
    
    # Find Error field column
    error_field_col = None
    for col in error_df.columns:
        print(f"Checking column: '{col}' - lower: '{str(col).lower()}'")
        if 'error field' in str(col).lower():
            error_field_col = col
            print(f"Found Error field column: {error_field_col}")
            break
    
    # If not found, try exact match
    if not error_field_col:
        for col in error_df.columns:
            if str(col).strip() == 'Error field':
                error_field_col = col
                print(f"Found Error field column (exact): {error_field_col}")
                break
    
    print(f"Final error_field_col: {error_field_col}")
    
    if error_field_col:
        # Get unique values from Error field column
        error_values = list(error_df[error_field_col].dropna().unique())
        dropdown_cell = worksheet.cell(row=2, column=2, value=error_values[0] if error_values else 'N/A')
    else:
        # Fallback: show column names for debugging
        error_values = list(error_df.columns)
        dropdown_cell = worksheet.cell(row=2, column=2, value=error_values[0] if error_values else 'N/A')
        error_field_col = error_values[0] if error_values else None
    
    dropdown_cell.fill = dropdown_fill
    dropdown_cell.border = thin_border
    dropdown_cell.alignment = center_alignment
    
    error_list = ','.join([str(e) for e in error_values])
    dv = DataValidation(type="list", formula1=f'"{error_list}"', allow_blank=False)
    worksheet.add_data_validation(dv)
    dv.add(dropdown_cell)
    
    # Write error data
    error_df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=3)
    
    # Find agent column
    agent_col = None
    for col in error_df.columns:
        if 'agent' in col.lower():
            agent_col = col
            break
    
    # Create chart data area
    chart_start_row = len(error_df) + 10
    
    # Write chart data for each error value
    if error_field_col and agent_col:
        for error_idx, error_val in enumerate(error_values):
            error_start_row = chart_start_row + error_idx * 15
            worksheet.cell(row=error_start_row, column=8, value=str(error_val))
            
            # Filter data for this error value and group by agent
            filtered_data = error_df[error_df[error_field_col] == error_val]
            agent_counts = filtered_data[agent_col].value_counts().reset_index()
            
            worksheet.cell(row=error_start_row + 1, column=8, value='Agent')
            worksheet.cell(row=error_start_row + 1, column=9, value='Count')
            
            for idx, (_, row) in enumerate(agent_counts.iterrows()):
                worksheet.cell(row=error_start_row + 2 + idx, column=8, value=str(row[agent_col]))
                worksheet.cell(row=error_start_row + 2 + idx, column=9, value=int(row['count']))
    
    # Create dynamic chart
    chart = BarChart()
    chart.title = '="Error: " & B2 & " vs Agent"'
    chart.x_axis.title = "Agent"
    chart.y_axis.title = "Count"
    chart.width = 15
    chart.height = 10
    
    # Dynamic chart using INDEX/MATCH formulas
    if error_field_col and agent_col and error_values:
        # Create dynamic data range that changes based on dropdown
        max_agents = len(error_df[agent_col].unique())
        
        # Use formulas to create dynamic chart data
        for i in range(max_agents):
            agent_formula = f'=INDEX(H:H, MATCH(B2, H:H, 0) + {i + 2})'
            count_formula = f'=INDEX(I:I, MATCH(B2, H:H, 0) + {i + 2})'
            worksheet.cell(row=chart_start_row + 50 + i, column=11, value=agent_formula)
            worksheet.cell(row=chart_start_row + 50 + i, column=12, value=count_formula)
        
        categories = Reference(worksheet, min_col=11, min_row=chart_start_row + 50, max_row=chart_start_row + 50 + max_agents - 1)
        data = Reference(worksheet, min_col=12, min_row=chart_start_row + 50, max_row=chart_start_row + 50 + max_agents - 1)
        
        chart.add_data(data, titles_from_data=False)
        chart.set_categories(categories)
        
        worksheet.add_chart(chart, "K4")
        
        # Hide formula columns
        worksheet.column_dimensions['K'].hidden = True
        worksheet.column_dimensions['L'].hidden = True
    
    # Set column widths
    for col in range(1, len(error_df.columns) + 1):
        worksheet.column_dimensions[chr(64 + col)].width = 15