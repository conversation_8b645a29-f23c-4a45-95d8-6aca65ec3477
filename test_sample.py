import pandas as pd
import openpyxl
from datetime import datetime, timedelta

# Create sample data for testing dropdown functionality
sample_data = {
    'Audit Date': [
        datetime.now() - timedelta(days=i) for i in range(10)
    ],
    'Scope': ['In Scope'] * 5 + ['Out of Scope'] * 5,
    'Error(Y/N)': ['Yes'] * 3 + ['No'] * 7,
    'Error field': [
        'Incorrect CPT',
        'Incorrect DOS',
        'Incorrect DX',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
    ],
    'Error Category': [
        'Typographical error',
        'Negligence',
        'Missed to check',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
    ],
    'Agent': [
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>'
    ],
    'Reporting To': [
        'Manager A',
        'Manager B',
        'Manager A',
        'Manager B',
        'Manager A',
        'Manager A',
        'Manager B',
        'Manager A',
        'Manager B',
        'Manager A'
    ],
    'QC Name': [
        'QC Person 1',
        'QC Person 2',
        'QC Person 1',
        'QC Person 3',
        'QC Person 2',
        'QC Person 1',
        'QC Person 2',
        'QC Person 1',
        'QC Person 3',
        'QC Person 2'
    ],
    'Transaction ID': [f'TXN{1000+i}' for i in range(10)],
    'Amount': [100.50 + i*10 for i in range(10)]
}

# Create DataFrame
df = pd.DataFrame(sample_data)

# Save to Excel
df.to_excel('sample_audit_data.xlsx', index=False)
print("Sample Excel file created: sample_audit_data.xlsx")
print("Columns included:", list(df.columns))
print("Sample data shape:", df.shape)
