from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Request, Form
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import pandas as pd
import io
from datetime import datetime
import openpyxl
from openpyxl.utils import get_column_letter
import numpy as np

app = FastAPI()

# Setup templates
templates = Jinja2Templates(directory="templates")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

processed_files = {}

@app.get("/", response_class=HTMLResponse)
async def upload_form(request: Request):
    return templates.TemplateResponse("upload.html", {"request": request})

@app.post("/upload")
async def upload_excel(request: Request, file: UploadFile = File(...)):
    if not file.filename.endswith(('.xlsx', '.xls')):
        return templates.TemplateResponse("upload.html", {
            "request": request, 
            "error": "Please upload an Excel file"
        })
    
    contents = await file.read()
    workbook = openpyxl.load_workbook(io.BytesIO(contents), data_only=False)
    worksheet = workbook.active
    
    data = []
    for row in worksheet.iter_rows():
        row_data = []
        for cell in row:
            if cell.value is None:
                row_data.append('')
            elif hasattr(cell, 'displayed_value'):
                row_data.append(str(cell.displayed_value))
            else:
                if cell.number_format and 'd' in cell.number_format.lower():
                    row_data.append(str(cell.value).split(' ')[0] if ' ' in str(cell.value) else str(cell.value))
                else:
                    row_data.append(str(cell.value))
        data.append(row_data)
    
    if not data:
        return templates.TemplateResponse("upload.html", {
            "request": request, 
            "error": "Excel file is empty"
        })
    
    headers = data[0]
    df_data = data[1:]
    df = pd.DataFrame(df_data, columns=headers)
    
    audit_col = None
    for col in df.columns:
        if col.lower().replace(' ', '').replace('_', '') == 'auditdate':
            audit_col = col
            break
    
    if audit_col is None:
        return templates.TemplateResponse("upload.html", {
            "request": request, 
            "error": "Audit Date column not found in the Excel file"
        })
    
    temp_dates = pd.to_datetime(df[audit_col], errors='coerce')
    latest_date = temp_dates.max()
    
    if pd.isna(latest_date):
        return templates.TemplateResponse("upload.html", {
            "request": request, 
            "error": "No valid dates found in Audit Date column"
        })
    
    mask = temp_dates == latest_date
    filtered_df = df[mask].copy()
    
    latest_weekday = latest_date.weekday()
    days_since_sunday = (latest_weekday + 1) % 7
    week_start = latest_date - pd.Timedelta(days=days_since_sunday)
    week_end = week_start + pd.Timedelta(days=6)
    week_mask = (temp_dates >= week_start) & (temp_dates <= week_end)
    weekly_df = df[week_mask].copy()
    
    latest_month_start = latest_date.replace(day=1)
    if latest_date.month == 12:
        latest_month_end = latest_date.replace(year=latest_date.year + 1, month=1, day=1) - pd.Timedelta(days=1)
    else:
        latest_month_end = latest_date.replace(month=latest_date.month + 1, day=1) - pd.Timedelta(days=1)
    month_mask = (temp_dates >= latest_month_start) & (temp_dates <= latest_month_end)
    monthly_df = df[month_mask].copy()
    
    # Pre-compute error data
    error_col = None
    error_df = pd.DataFrame()
    for col in df.columns:
        if col.lower().replace(' ', '').replace('_', '').replace('(', '').replace(')', '') in ['errory/n', 'erroryn', 'error']:
            error_col = col
            error_mask = df[error_col].astype(str).str.lower().str.strip() == 'yes'
            error_df = df[error_mask].copy()
            break
    
    file_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
    
    processed_files[file_id] = {
        'original': df,
        'filtered': filtered_df,
        'weekly': weekly_df,
        'monthly': monthly_df,
        'error': error_df,
        'latest_date': latest_date,
        'audit_col': audit_col,
        'error_col': error_col
    }
    
    # Process data for display
    processed_data = {
        "filename": file.filename,
        "file_id": file_id,
        "latest_audit_date": latest_date.strftime("%Y-%m-%d"),
        "audit_column_found": audit_col,
        "total_original_rows": len(df),
        "filtered_rows": len(filtered_df),
        "columns": list(df.columns)
    }
    
    return templates.TemplateResponse("results.html", {
        "request": request,
        "data": processed_data
    })


@app.get("/download/{file_id}")
async def download_processed_file(file_id: str):
    if file_id not in processed_files:
        return {"error": "File not found"}
    
    # Get pre-computed data
    data = processed_files[file_id]
    filtered_df = data['filtered']
    weekly_df = data['weekly']
    monthly_df = data['monthly']
    error_df = data['error']
    original_df = data['original']
    latest_date = data['latest_date']
    error_col = data['error_col']
    
    # Prepare data for weekly/monthly/daily analysis
    analysis_df = original_df.copy()
    
    # Find Audited transaction column
    audited_transaction_col = None
    for col in original_df.columns:
        col_lower = col.lower().replace(' ', '').replace('_', '')
        if 'audited' in col_lower and 'transaction' in col_lower:
            audited_transaction_col = col
            break
        elif col_lower == 'auditedtransaction':
            audited_transaction_col = col
            break
    
    # Find Error count column (different from Error(Y/N) column)
    error_count_col = None
    for col in original_df.columns:
        if col.lower().strip() == 'error' and col != error_col:
            error_count_col = col
            break
    
    # Add required columns for analysis
    if audited_transaction_col:
        analysis_df['Audited transaction'] = pd.to_numeric(analysis_df[audited_transaction_col], errors='coerce').fillna(1)
    else:
        analysis_df['Audited transaction'] = 1
    
    # Calculate error count based on Error(Y/N) and error count column
    if error_count_col and error_col:
        analysis_df['Has_Error'] = pd.to_numeric(analysis_df[error_count_col], errors='coerce').fillna(0) * (analysis_df[error_col].astype(str).str.lower().str.strip().apply(lambda x: 1 if x == 'yes' else 0))
    elif error_col:
        analysis_df['Has_Error'] = analysis_df[error_col].astype(str).str.lower().str.strip().apply(lambda x: 1 if x == 'yes' else 0)
    else:
        analysis_df['Has_Error'] = 0
    
    # Find Sampling column
    sampling_col = None
    for col in original_df.columns:
        if 'sampling' in col.lower():
            sampling_col = col
            break
    
    analysis_df['Sampling_Count'] = analysis_df[sampling_col].astype(str).str.lower().str.strip().apply(lambda x: 1 if x == 'yes' else 0) if sampling_col else 0
    
    # Find Agent and Team Lead columns
    agent_col = None
    team_lead_col = None
    
    for col in original_df.columns:
        if col.lower().strip() == 'agent':
            agent_col = col
            break
    
    for col in original_df.columns:
        if 'reporting' in col.lower() or 'team' in col.lower() or 'lead' in col.lower():
            team_lead_col = col
            break
    
    if agent_col:
        analysis_df['Agent'] = analysis_df[agent_col]
        analysis_df['Reporting To'] = analysis_df[team_lead_col] if team_lead_col else 'N/A'
        
        # Convert audit date for analysis
        audit_col_name = data['audit_col']
        analysis_df['Audit Date'] = pd.to_datetime(analysis_df[audit_col_name], errors='coerce')
        
        # Prepare weekly, monthly and daily data
        weekly_data, max_week = prepare_weekly_data_dynamic(analysis_df)
        monthly_data, months = prepare_monthly_data(analysis_df)
        daily_data, unique_dates = prepare_daily_data(analysis_df)
    
    # Create Error Analysis data (existing logic)
    error_analysis_df = pd.DataFrame()
    if agent_col:
        current_error_col = error_col
        if current_error_col:
            agent_stats = []
            agent_groups = {}
            
            for agent_name in original_df[agent_col].unique():
                if str(agent_name).strip() and str(agent_name) != 'nan' and str(agent_name) != '':
                    normalized_name = str(agent_name).strip().lower()
                    
                    if normalized_name not in agent_groups:
                        agent_groups[normalized_name] = {
                            'display_name': str(agent_name).strip(),
                            'data': []
                        }
                    
                    agent_data = original_df[original_df[agent_col].astype(str).str.lower().str.strip() == normalized_name]
                    agent_groups[normalized_name]['data'].append(agent_data)
            
            for normalized_name, group_info in agent_groups.items():
                combined_data = pd.concat(group_info['data'], ignore_index=True)
                audited_lines = len(combined_data)
                
                error_count = len(combined_data[combined_data[current_error_col].astype(str).str.lower().str.strip() == 'yes'])
                
                if audited_lines > 0:
                    accuracy_pct = round((1 - (error_count / audited_lines)) * 100, 2)
                    error_pct = round(100 - accuracy_pct, 2)
                else:
                    accuracy_pct = 0
                    error_pct = 0
                
                agent_stats.append({
                    'Agent Name': group_info['display_name'],
                    'Audited Lines': audited_lines,
                    'Error': error_count,
                    'Accuracy%': accuracy_pct,
                    'Error %': error_pct
                })
            
            if agent_stats:
                error_analysis_df = pd.DataFrame(agent_stats)
    
    # Create Excel file
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        daily_sheet_name = f"{latest_date.strftime('%B-%Y')}-Daily"
        weekly_sheet_name = f"{latest_date.strftime('%B-%Y')}-Weekly"
        monthly_sheet_name = f"{latest_date.strftime('%B-%Y')}-Monthly"
        therapy_header = f"Therapy_{latest_date.strftime('%B-%Y')}"
        
        # Write enhanced sheets with Daily first if agent data available
        if agent_col and not weekly_data.empty:
            create_daily_summary_sheet_simple(writer, daily_data, unique_dates, daily_sheet_name, analysis_df, original_df)
            create_weekly_summary_sheet_simple(writer, weekly_data, max_week, weekly_sheet_name, analysis_df, original_df)
            create_monthly_summary_sheet_simple(writer, monthly_data, months, monthly_sheet_name, analysis_df, original_df)
        else:
            # Fallback to basic sheets
            weekly_df.to_excel(writer, sheet_name=weekly_sheet_name, index=False)
            monthly_df.to_excel(writer, sheet_name=monthly_sheet_name, index=False)
        
        if not error_df.empty:
            error_df.to_excel(writer, sheet_name="Error Details", index=False)
            
            # Add chart to Error Details sheet
            from openpyxl.chart import BarChart, Reference
            from openpyxl.worksheet.datavalidation import DataValidation
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            
            error_sheet = writer.sheets["Error Details"]
            
            # Find error field column - look for exact match first
            error_field_col = None
            agent_col = None
            
            # First try exact match for "Error field"
            for col in error_df.columns:
                if str(col).strip() == 'Error field':
                    error_field_col = col
                    break
            
            # If not found, try case-insensitive match
            if not error_field_col:
                for col in error_df.columns:
                    if str(col).lower().strip() == 'error field':
                        error_field_col = col
                        break
            
            # Find agent column
            for col in error_df.columns:
                if col.lower().strip() == 'agent':
                    agent_col = col
                    break
            
            if error_field_col and agent_col:
                # Get unique error fields
                unique_errors = error_df[error_field_col].dropna().unique()
                error_list = [str(e) for e in unique_errors if str(e).strip() != '' and str(e) != 'nan']
                
                # Add dropdown
                dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
                thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
                
                error_sheet.cell(row=1, column=len(error_df.columns)+2, value='Select Error:')
                dropdown_cell = error_sheet.cell(row=1, column=len(error_df.columns)+3, value=error_list[0] if error_list else 'No Errors')
                dropdown_cell.fill = dropdown_fill
                dropdown_cell.border = thin_border
                
                # Add instruction text
                instruction_cell = error_sheet.cell(row=2, column=len(error_df.columns)+2, value='Chart updates automatically')
                instruction_cell.font = Font(size=9, italic=True)
                instruction_cell.alignment = Alignment(horizontal='left', vertical='center')
                
                if error_list:
                    # Split into chunks if too many options for Excel dropdown limit
                    if len(error_list) <= 255:  # Excel dropdown limit
                        error_options = ','.join(error_list)
                        dv = DataValidation(type="list", formula1=f'"{error_options}"', allow_blank=False)
                        dv.add(dropdown_cell)
                        error_sheet.add_data_validation(dv)
                    else:
                        # If too many options, create a range reference instead
                        option_start_row = len(error_df) + 50
                        for i, option in enumerate(error_list):
                            error_sheet.cell(row=option_start_row + i, column=1, value=option)
                        
                        option_range = f'A{option_start_row}:A{option_start_row + len(error_list) - 1}'
                        dv = DataValidation(type="list", formula1=option_range, allow_blank=False)
                        dv.add(dropdown_cell)
                        error_sheet.add_data_validation(dv)
                
                # Create dynamic chart data area
                chart_start_row = len(error_df) + 5
                
                # Store all error field data in hidden area for lookup
                for idx, error_field in enumerate(error_list):
                    filtered_data = error_df[error_df[error_field_col] == error_field]
                    agent_counts = filtered_data[agent_col].value_counts().reset_index()
                    agent_counts.columns = ['Agent', 'Count']
                    
                    # Store error field header in hidden area
                    error_sheet.cell(row=chart_start_row + idx * 15, column=len(error_df.columns)+5, value=error_field)
                    
                    # Store agent data for this error field
                    for i, ac_row in agent_counts.iterrows():
                        if i < 10:  # Limit to 10 agents
                            error_sheet.cell(row=chart_start_row + idx * 15 + i + 1, column=len(error_df.columns)+5, value=str(ac_row['Agent']))
                            error_sheet.cell(row=chart_start_row + idx * 15 + i + 1, column=len(error_df.columns)+6, value=int(ac_row['Count']))
                
                # Create dynamic chart data that updates based on dropdown selection
                chart_data_start = chart_start_row + len(error_list) * 15 + 5
                
                from openpyxl.utils import get_column_letter
                dropdown_col = get_column_letter(len(error_df.columns) + 3)
                lookup_col = get_column_letter(len(error_df.columns) + 5)
                count_col = get_column_letter(len(error_df.columns) + 6)
                chart_agent_col = get_column_letter(len(error_df.columns) + 7)
                chart_count_col = get_column_letter(len(error_df.columns) + 8)
                
                # Create formulas that lookup data based on dropdown selection
                for i in range(10):  # Show up to 10 agents
                    # Agent name formula
                    agent_formula = f'=IFERROR(INDEX({lookup_col}{chart_start_row + 1}:{lookup_col}{chart_start_row + len(error_list) * 15}, MATCH({dropdown_col}1, {lookup_col}{chart_start_row}:{lookup_col}{chart_start_row + len(error_list) * 15}, 0) + {i}), "")'
                    error_sheet.cell(row=chart_data_start + i, column=len(error_df.columns)+7, value=agent_formula)
                    
                    # Count formula
                    count_formula = f'=IFERROR(INDEX({count_col}{chart_start_row + 1}:{count_col}{chart_start_row + len(error_list) * 15}, MATCH({dropdown_col}1, {lookup_col}{chart_start_row}:{lookup_col}{chart_start_row + len(error_list) * 15}, 0) + {i}), 0)'
                    error_sheet.cell(row=chart_data_start + i, column=len(error_df.columns)+8, value=count_formula)
                
                # Create chart that references the dynamic data
                chart = BarChart()
                chart.title = f'="Error Field vs Agent - " & {dropdown_col}1'
                chart.x_axis.title = "Agents"
                chart.y_axis.title = "Error Count"
                chart.width = 15
                chart.height = 10
                
                agent_ref = Reference(error_sheet, min_col=len(error_df.columns)+7, min_row=chart_data_start, max_row=chart_data_start+9)
                count_ref = Reference(error_sheet, min_col=len(error_df.columns)+8, min_row=chart_data_start, max_row=chart_data_start+9)
                chart.add_data(count_ref, titles_from_data=False)
                chart.set_categories(agent_ref)
                
                # Position chart to the right of data
                chart_col = get_column_letter(len(error_df.columns) + 10)
                error_sheet.add_chart(chart, f"{chart_col}3")
                
                # Hide the lookup data columns
                error_sheet.column_dimensions[get_column_letter(len(error_df.columns) + 5)].hidden = True
                error_sheet.column_dimensions[get_column_letter(len(error_df.columns) + 6)].hidden = True
                error_sheet.column_dimensions[get_column_letter(len(error_df.columns) + 7)].hidden = True
                error_sheet.column_dimensions[get_column_letter(len(error_df.columns) + 8)].hidden = True
                
                # Hide the lookup data rows
                for row_idx in range(chart_start_row, chart_data_start + 10):
                    error_sheet.row_dimensions[row_idx].hidden = True
        
        if not error_analysis_df.empty:
            total_audited = error_analysis_df['Audited Lines'].sum()
            total_errors = error_analysis_df['Error'].sum()
            total_accuracy = round((1 - (total_errors / total_audited)) * 100, 2) if total_audited > 0 else 0
            total_error_pct = round(100 - total_accuracy, 2)
            
            total_row = pd.DataFrame({
                'Agent Name': ['Total'],
                'Audited Lines': [total_audited],
                'Error': [total_errors],
                'Accuracy%': [total_accuracy],
                'Error %': [total_error_pct]
            })
            
            error_analysis_with_total = pd.concat([error_analysis_df, total_row], ignore_index=True)
            error_analysis_with_total.to_excel(writer, sheet_name="Error Analysis", index=False, startrow=1)
            
            worksheet = writer.sheets["Error Analysis"]
            worksheet.cell(row=1, column=1, value=therapy_header)
        
        original_df.to_excel(writer, sheet_name="Raw Data", index=False)

    # Reorder sheets to put Daily first
    workbook = openpyxl.load_workbook(output)
    if daily_sheet_name in workbook.sheetnames:
        daily_sheet = workbook[daily_sheet_name]
        workbook.move_sheet(daily_sheet, offset=-len(workbook.sheetnames)+1)

    # Add dropdown functionality to Raw Data sheet AFTER reloading workbook
    add_raw_data_dropdowns_to_workbook(workbook, original_df)

    # Save the reordered workbook
    output.seek(0)
    output.truncate(0)
    workbook.save(output)
    output.seek(0)
    
    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={"Content-Disposition": f"attachment; filename=processed_{file_id}.xlsx"}
    )


def add_raw_data_dropdowns_to_workbook(workbook, original_df):
    """Add dropdown functionality to specific columns in Raw Data sheet"""
    from openpyxl.worksheet.datavalidation import DataValidation
    from openpyxl.utils import get_column_letter

    # Get the Raw Data worksheet
    raw_data_sheet = workbook["Raw Data"]

    # Define dropdown options for each column
    dropdown_options = {
        'Scope': ['In Scope', 'Out of Scope'],
        'Error(Y/N)': ['Yes', 'No'],
        'Error field': [
            'Incorrect 31 and 32 box details',
            'Incorrect Auth#',
            'Incorrect billed amount',
            'Incorrect billing provider details',
            'Incorrect clubbed the session',
            'Incorrect copay amount',
            'Incorrect CPT',
            'Incorrect DOS',
            'Incorrect DX',
            'Incorrect Excel in path',
            'Incorrect HCFA',
            'Incorrect insurance name',
            'Incorrect Modifier',
            'Incorrect pending status in AR tool',
            'Incorrect pending comments',
            'Incorrect POS',
            'Incorrect Units',
            'Incorrect Until Date',
            'Incorrectly billed session',
            'Incorrectly roundoff',
            'Missed to check in motivity',
            'Missed to check MUE',
            'Missed to check old DOS',
            'Missed to dummy the session',
            'Pending line missed in AR Tool',
            'Referring provider details missed',
            'There is no appointment excel in path',
            'Updates not listed in SOP'
        ],
        'Error Category': [
            'Typographical error',
            'Negligence',
            'Missed to check',
            'Updates Missed'
        ]
    }

    # Find column indices for the specified columns
    column_indices = {}
    print(f"DEBUG: Original DataFrame columns: {list(original_df.columns)}")

    for col_idx, col_name in enumerate(original_df.columns, 1):
        col_name_clean = str(col_name).strip()

        # Check for exact matches and variations
        if col_name_clean in dropdown_options:
            column_indices[col_name_clean] = col_idx
        elif col_name_clean.lower() == 'scope':
            column_indices['Scope'] = col_idx
        elif col_name_clean.lower() in ['error(y/n)', 'error (y/n)', 'erroryn', 'error yn']:
            column_indices['Error(Y/N)'] = col_idx
        elif col_name_clean.lower() in ['error field', 'errorfield']:
            column_indices['Error field'] = col_idx
        elif col_name_clean.lower() in ['error category', 'errorcategory']:
            column_indices['Error Category'] = col_idx
        elif col_name_clean.lower() == 'agent':
            column_indices['Agent'] = col_idx
        elif col_name_clean.lower() in ['reporting to', 'reportingto', 'team lead']:
            column_indices['Reporting To'] = col_idx
        elif col_name_clean.lower() in ['qc name', 'qcname', 'qc']:
            column_indices['QC Name'] = col_idx

    print(f"DEBUG: Found column indices: {column_indices}")

    # Apply dropdowns to each found column
    for col_name, col_idx in column_indices.items():
        if col_name in dropdown_options:
            options = dropdown_options[col_name]
            print(f"DEBUG: Adding dropdown for {col_name} in column {col_idx} with {len(options)} options")

            # Create dropdown validation
            if len(options) <= 255:  # Excel dropdown limit
                options_str = ','.join([f'"{opt}"' for opt in options])
                dv = DataValidation(type="list", formula1=options_str, allow_blank=True)
                print(f"DEBUG: Using direct formula for {col_name}")
            else:
                # For longer lists, create a range reference
                options_start_row = len(original_df) + 10
                col_letter = get_column_letter(col_idx + 10)  # Use columns to the right

                for i, option in enumerate(options):
                    raw_data_sheet.cell(row=options_start_row + i, column=col_idx + 10, value=option)

                options_range = f'{col_letter}{options_start_row}:{col_letter}{options_start_row + len(options) - 1}'
                dv = DataValidation(type="list", formula1=options_range, allow_blank=True)
                print(f"DEBUG: Using range reference for {col_name}: {options_range}")

                # Hide the options column
                raw_data_sheet.column_dimensions[col_letter].hidden = True

            dv.error = f'Please select a valid {col_name} option'
            dv.errorTitle = f'Invalid {col_name}'

            # Apply to all data rows (excluding header)
            col_letter = get_column_letter(col_idx)
            data_range = f'{col_letter}2:{col_letter}{len(original_df) + 1}'
            print(f"DEBUG: Applying dropdown to range {data_range}")
            dv.ranges.add(data_range)

            raw_data_sheet.add_data_validation(dv)
            print(f"DEBUG: Successfully added dropdown for {col_name}")

    # For Agent, Reporting To, and QC Name columns, create dropdowns from existing data
    dynamic_columns = ['Agent', 'Reporting To', 'QC Name']
    for col_name in dynamic_columns:
        if col_name in column_indices:
            col_idx = column_indices[col_name]

            # Get unique values from the column (excluding empty/null values)
            unique_values = original_df.iloc[:, col_idx-1].dropna().astype(str)
            unique_values = unique_values[unique_values.str.strip() != '']
            unique_values = unique_values[unique_values != 'nan']
            unique_values = sorted(unique_values.unique())

            if unique_values:
                if len(unique_values) <= 255:  # Excel dropdown limit
                    options_str = ','.join([f'"{val}"' for val in unique_values])
                    dv = DataValidation(type="list", formula1=options_str, allow_blank=True)
                else:
                    # For longer lists, create a range reference
                    options_start_row = len(original_df) + 50
                    col_letter = get_column_letter(col_idx + 15)  # Use columns further to the right

                    for i, value in enumerate(unique_values):
                        raw_data_sheet.cell(row=options_start_row + i, column=col_idx + 15, value=value)

                    options_range = f'{col_letter}{options_start_row}:{col_letter}{options_start_row + len(unique_values) - 1}'
                    dv = DataValidation(type="list", formula1=options_range, allow_blank=True)

                    # Hide the options column
                    raw_data_sheet.column_dimensions[col_letter].hidden = True

                dv.error = f'Please select a valid {col_name}'
                dv.errorTitle = f'Invalid {col_name}'

                # Apply to all data rows (excluding header)
                col_letter = get_column_letter(col_idx)
                data_range = f'{col_letter}2:{col_letter}{len(original_df) + 1}'
                dv.ranges.add(data_range)

                raw_data_sheet.add_data_validation(dv)


def add_qc_summary_to_sheet(worksheet, full_data, start_row=None):
    """Add QC Summary section to worksheet"""
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    
    # Define styles
    title_font = Font(bold=True, size=12)
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    
    header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
    total_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")
    
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center')
    left_alignment = Alignment(horizontal='left', vertical='center')
    
    # Determine start row and column
    if start_row is None:
        start_row = 6  # Default start row
    qc_col = 8  # Column H for QC Name
    count_col = 9  # Column I for Count
    
    # Find QC Name column with more flexible matching
    qc_data_col = None
    possible_names = ['qc name', 'qc_name', 'qc', 'quality control', 'quality_checker']
    
    for col in full_data.columns:
        col_lower = str(col).strip().lower().replace(' ', '').replace('_', '').replace('-', '')
        for name in possible_names:
            if name.replace(' ', '').replace('_', '').replace('-', '') in col_lower:
                qc_data_col = col
                break
        if qc_data_col:
            break
    
    # Add QC Summary title
    title_cell = worksheet.cell(row=start_row - 1, column=qc_col, value="QC Summary")
    title_cell.font = title_font
    
    # Add headers
    header1 = worksheet.cell(row=start_row, column=qc_col, value="QC Name")
    header1.font = header_font
    header1.fill = header_fill
    header1.border = thin_border
    header1.alignment = center_alignment
    
    header2 = worksheet.cell(row=start_row, column=count_col, value="Count")
    header2.font = header_font
    header2.fill = header_fill
    header2.border = thin_border
    header2.alignment = center_alignment
    
    if qc_data_col:
        # Clean and count QC data
        qc_data = full_data[qc_data_col].astype(str)
        qc_data_clean = qc_data[
            (~qc_data.isin(['nan', 'None', 'NaN'])) & 
            (qc_data.str.strip() != '')
        ]
        
        if len(qc_data_clean) > 0:
            qc_counts = qc_data_clean.value_counts().reset_index()
            qc_counts.columns = ['QC Name', 'Count']
            
            # Write data rows
            for i, (_, qc_row) in enumerate(qc_counts.iterrows()):
                current_row = start_row + 1 + i
                
                name_cell = worksheet.cell(row=current_row, column=qc_col, value=str(qc_row['QC Name']))
                name_cell.font = data_font
                name_cell.border = thin_border
                name_cell.alignment = left_alignment
                
                count_cell = worksheet.cell(row=current_row, column=count_col, value=int(qc_row['Count']))
                count_cell.font = data_font
                count_cell.border = thin_border
                count_cell.alignment = center_alignment
            
            # Add total row
            total_row = start_row + 1 + len(qc_counts)
            total_count = int(qc_counts['Count'].sum())
            
            total_name_cell = worksheet.cell(row=total_row, column=qc_col, value="Total")
            total_name_cell.font = Font(bold=True, size=10)
            total_name_cell.fill = total_fill
            total_name_cell.border = thin_border
            total_name_cell.alignment = left_alignment
            
            total_count_cell = worksheet.cell(row=total_row, column=count_col, value=total_count)
            total_count_cell.font = Font(bold=True, size=10)
            total_count_cell.fill = total_fill
            total_count_cell.border = thin_border
            total_count_cell.alignment = center_alignment
            
            # Set column widths
            max_name_length = max(len(str(name)) for name in qc_counts['QC Name'])
            worksheet.column_dimensions[get_column_letter(qc_col)].width = max(15, max_name_length + 2)
            worksheet.column_dimensions[get_column_letter(count_col)].width = 10
        else:
            # No valid QC data found
            no_data_cell = worksheet.cell(row=start_row + 1, column=qc_col, value="No QC data found")
            no_data_cell.font = data_font
            no_data_cell.border = thin_border
            no_data_cell.alignment = left_alignment
            
            count_cell = worksheet.cell(row=start_row + 1, column=count_col, value=0)
            count_cell.font = data_font
            count_cell.border = thin_border
            count_cell.alignment = center_alignment
    else:
        # QC column not found
        no_col_cell = worksheet.cell(row=start_row + 1, column=qc_col, value="QC Name column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border
        no_col_cell.alignment = left_alignment
        
        count_cell = worksheet.cell(row=start_row + 1, column=count_col, value=0)
        count_cell.font = data_font
        count_cell.border = thin_border
        count_cell.alignment = center_alignment


def prepare_weekly_data_dynamic(data):
    data['Week_Num'] = ((data['Audit Date'] - data['Audit Date'].min()).dt.days // 7) + 1
    max_week = int(data['Week_Num'].max())
    
    weekly_summary = data.groupby(['Agent', 'Reporting To', 'Week_Num']).agg({
        'Audited transaction': 'sum',
        'Has_Error': 'sum',
        'Sampling_Count': 'sum'
    }).reset_index()
    
    weekly_summary['Score'] = np.where(
        weekly_summary['Audited transaction'] > 0,
        ((weekly_summary['Audited transaction'] - weekly_summary['Has_Error']) / 
         weekly_summary['Audited transaction'] * 100).round(2),
        100.0
    )
    
    agents = data[['Agent', 'Reporting To']].drop_duplicates()
    result_data = []
    
    for _, agent in agents.iterrows():
        row = {'Agent Name': agent['Agent'], 'Team Lead': agent['Reporting To']}
        
        for week in range(1, max_week + 1):
            week_data = weekly_summary[
                (weekly_summary['Agent'] == agent['Agent']) & 
                (weekly_summary['Week_Num'] == week)
            ]
            
            if len(week_data) > 0:
                row[f'Week_{week}_Audited'] = int(week_data['Audited transaction'].iloc[0])
                row[f'Week_{week}_Errors'] = int(week_data['Has_Error'].iloc[0])
                row[f'Week_{week}_Score'] = float(week_data['Score'].iloc[0])
                row[f'Week_{week}_Productivity'] = int(week_data['Sampling_Count'].iloc[0])
            else:
                row[f'Week_{week}_Audited'] = 0
                row[f'Week_{week}_Errors'] = 0
                row[f'Week_{week}_Score'] = 100.00
                row[f'Week_{week}_Productivity'] = 0
        
        result_data.append(row)
    
    total_row = {'Agent Name': 'Total', 'Team Lead': ''}
    for week in range(1, max_week + 1):
        week_total_data = weekly_summary[weekly_summary['Week_Num'] == week]
        total_audited = int(week_total_data['Audited transaction'].sum())
        total_errors = int(week_total_data['Has_Error'].sum())
        total_score = ((total_audited - total_errors) / total_audited * 100) if total_audited > 0 else 100.00
        
        total_productivity = int(week_total_data['Sampling_Count'].sum())
        total_row[f'Week_{week}_Audited'] = total_audited
        total_row[f'Week_{week}_Errors'] = total_errors
        total_row[f'Week_{week}_Score'] = round(total_score, 2)
        total_row[f'Week_{week}_Productivity'] = total_productivity
    
    result_data.append(total_row)
    return pd.DataFrame(result_data), max_week

def prepare_monthly_data(data):
    data = data.copy()
    data['Month_Year'] = data['Audit Date'].dt.to_period('M').astype(str)
    
    monthly = data.groupby(['Agent', 'Reporting To', 'Month_Year']).agg({
        'Audited transaction': 'sum',
        'Has_Error': 'sum',
        'Sampling_Count': 'sum'
    }).reset_index()
    
    monthly['Score'] = np.where(
        monthly['Audited transaction'] > 0,
        ((monthly['Audited transaction'] - monthly['Has_Error']) / 
         monthly['Audited transaction'] * 100).round(2),
        100.0
    )
    
    agents = data[['Agent','Reporting To']].drop_duplicates()
    months = sorted(monthly['Month_Year'].unique())
    
    rows = []
    for _, ag in agents.iterrows():
        row = {'Agent Name':ag['Agent'], 'Team Lead':ag['Reporting To']}
        for m in months:
            slice_ = monthly[
                (monthly['Agent']==ag['Agent']) & 
                (monthly['Month_Year']==m)
            ]
            if not slice_.empty:
                row[f'{m}_Audited'] = int(slice_['Audited transaction'].iloc[0])
                row[f'{m}_Errors']  = int(slice_['Has_Error'].iloc[0])
                row[f'{m}_Score']   = float(slice_['Score'].iloc[0])
                row[f'{m}_Productivity'] = int(slice_['Sampling_Count'].iloc[0])
            else:
                row[f'{m}_Audited'] = 0
                row[f'{m}_Errors']  = 0
                row[f'{m}_Score']   = 100.00
                row[f'{m}_Productivity'] = 0
        rows.append(row)
    
    # Add total row
    total_row = {'Agent Name': 'Total', 'Team Lead': ''}
    for m in months:
        month_total_data = monthly[monthly['Month_Year'] == m]
        total_audited = int(month_total_data['Audited transaction'].sum())
        total_errors = int(month_total_data['Has_Error'].sum())
        total_score = ((total_audited - total_errors) / total_audited * 100) if total_audited > 0 else 100.00
        total_productivity = int(month_total_data['Sampling_Count'].sum())
        
        total_row[f'{m}_Audited'] = total_audited
        total_row[f'{m}_Errors'] = total_errors
        total_row[f'{m}_Score'] = round(total_score, 2)
        total_row[f'{m}_Productivity'] = total_productivity
    rows.append(total_row)
    
    return pd.DataFrame(rows), months

def add_qc_dropdown_functionality(worksheet, full_data, qc_start_col, qc_start_row, data_start_row, period_data_size, period_type):
    """Add dropdown functionality to QC Summary section"""
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    from openpyxl.utils import get_column_letter
    import pandas as pd
    
    # Define styles
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    center_alignment = Alignment(horizontal='center', vertical='center')
    
    # Find QC column
    qc_col = None
    for col in full_data.columns:
        if 'qc' in str(col).lower():
            qc_col = col
            break
    
    # QC Summary title
    worksheet.merge_cells(start_row=qc_start_row, start_column=qc_start_col, 
                         end_row=qc_start_row, end_column=qc_start_col+1)
    qc_title = worksheet.cell(row=qc_start_row, column=qc_start_col, value="QC Summary")
    qc_title.font = Font(bold=True, size=12)
    qc_title.fill = title_fill
    qc_title.alignment = center_alignment
    qc_title.border = thin_border

    # QC dropdown for period selection
    qc_dropdown_cell = worksheet.cell(row=qc_start_row+1, column=qc_start_col, value=period_type)
    qc_dropdown_cell.font = Font(bold=True, size=10)
    qc_dropdown_cell.fill = dropdown_fill
    qc_dropdown_cell.alignment = center_alignment
    qc_dropdown_cell.border = thin_border
    
    qc_dv = DataValidation(type="list", formula1='"Daily,Weekly,Monthly"', allow_blank=False)
    qc_dv.add(qc_dropdown_cell)
    worksheet.add_data_validation(qc_dv)

    # QC headers
    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").border = thin_border
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").border = thin_border

    if qc_col:
        # Create QC data for all periods and store in hidden rows
        qc_data_start_row = data_start_row + period_data_size + 10
        
        # Daily QC data
        daily_qc_data = full_data[qc_col].dropna().astype(str)
        daily_qc_data = daily_qc_data[daily_qc_data.str.strip() != '']
        daily_qc_data = daily_qc_data[daily_qc_data != 'nan']
        
        daily_qc_counts = pd.DataFrame()
        if len(daily_qc_data) > 0:
            daily_qc_counts = daily_qc_data.value_counts().reset_index()
            daily_qc_counts.columns = ['QC Name', 'Count']
            
            # Store daily QC data in hidden rows
            for i, qc_row in daily_qc_counts.iterrows():
                worksheet.cell(row=qc_data_start_row+1+i, column=qc_start_col+3, value=str(qc_row['QC Name']))
                worksheet.cell(row=qc_data_start_row+1+i, column=qc_start_col+4, value=int(qc_row['Count']))
        
        # Weekly QC data
        weekly_qc_start = qc_data_start_row + 15
        full_data_copy = full_data.copy()
        full_data_copy['Week'] = full_data_copy['Audit Date'].dt.to_period('W')
        weekly_qc_data = full_data_copy.groupby('Week')[qc_col].apply(lambda x: x.dropna().astype(str).value_counts()).reset_index()
        
        weekly_qc_counts = pd.DataFrame()
        if len(weekly_qc_data) > 0:
            weekly_qc_counts = weekly_qc_data.groupby(qc_col)['count'].sum().reset_index()
            weekly_qc_counts.columns = ['QC Name', 'Count']
            
            for i, qc_row in weekly_qc_counts.iterrows():
                worksheet.cell(row=weekly_qc_start+1+i, column=qc_start_col+3, value=str(qc_row['QC Name']))
                worksheet.cell(row=weekly_qc_start+1+i, column=qc_start_col+4, value=int(qc_row['Count']))
        
        # Monthly QC data
        monthly_qc_start = weekly_qc_start + 15
        full_data_copy['Month'] = full_data_copy['Audit Date'].dt.to_period('M')
        monthly_qc_data = full_data_copy.groupby('Month')[qc_col].apply(lambda x: x.dropna().astype(str).value_counts()).reset_index()
        
        monthly_qc_counts = pd.DataFrame()
        if len(monthly_qc_data) > 0:
            monthly_qc_counts = monthly_qc_data.groupby(qc_col)['count'].sum().reset_index()
            monthly_qc_counts.columns = ['QC Name', 'Count']
            
            for i, qc_row in monthly_qc_counts.iterrows():
                worksheet.cell(row=monthly_qc_start+1+i, column=qc_start_col+3, value=str(qc_row['QC Name']))
                worksheet.cell(row=monthly_qc_start+1+i, column=qc_start_col+4, value=int(qc_row['Count']))
        
        # Display formulas that reference the hidden data based on dropdown selection
        max_qc_rows = max(len(daily_qc_counts) if len(daily_qc_counts) > 0 else 0, 10)
        
        for i in range(max_qc_rows):
            # QC Name formula
            name_formula = f'=IF({qc_dropdown_cell.coordinate}="Daily",INDEX({get_column_letter(qc_start_col+3)}:{get_column_letter(qc_start_col+3)},{qc_data_start_row+1+i}),IF({qc_dropdown_cell.coordinate}="Weekly",INDEX({get_column_letter(qc_start_col+3)}:{get_column_letter(qc_start_col+3)},{weekly_qc_start+1+i}),INDEX({get_column_letter(qc_start_col+3)}:{get_column_letter(qc_start_col+3)},{monthly_qc_start+1+i})))'
            name_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col, value=name_formula)
            name_cell.font = data_font
            name_cell.border = thin_border
            name_cell.alignment = Alignment(horizontal='left', vertical='center')
            
            # Count formula
            count_formula = f'=IF({qc_dropdown_cell.coordinate}="Daily",INDEX({get_column_letter(qc_start_col+4)}:{get_column_letter(qc_start_col+4)},{qc_data_start_row+1+i}),IF({qc_dropdown_cell.coordinate}="Weekly",INDEX({get_column_letter(qc_start_col+4)}:{get_column_letter(qc_start_col+4)},{weekly_qc_start+1+i}),INDEX({get_column_letter(qc_start_col+4)}:{get_column_letter(qc_start_col+4)},{monthly_qc_start+1+i})))'
            count_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col+1, value=count_formula)
            count_cell.font = data_font
            count_cell.border = thin_border
            count_cell.alignment = center_alignment
        
        # Hide the data rows
        for row_idx in range(qc_data_start_row, monthly_qc_start + 20):
            worksheet.row_dimensions[row_idx].hidden = True
            
    else:
        no_col_cell = worksheet.cell(row=qc_start_row+3, column=qc_start_col, value="QC column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border

    # Set column widths
    worksheet.column_dimensions[get_column_letter(qc_start_col)].width = 15
    worksheet.column_dimensions[get_column_letter(qc_start_col+1)].width = 10

def create_weekly_summary_sheet_simple(writer, weekly_data, max_week, sheet_name, data, full_data):
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    from openpyxl.utils import get_column_letter
    import pandas as pd
    
    # Get the workbook and create worksheet
    workbook = writer.book
    worksheet = workbook.create_sheet(title=sheet_name)
    
    # Define styles
    title_font = Font(bold=True, size=14)
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    total_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")
    
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Get date range
    start_date = data['Audit Date'].min().strftime('%m/%d/%Y')
    end_date = data['Audit Date'].max().strftime('%m/%d/%Y')
    
    # Title row
    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=6)
    title_cell = worksheet.cell(row=1, column=1, value=f'Weekly Audit_Charge({start_date} - {end_date})')
    title_cell.font = title_font
    title_cell.fill = title_fill
    title_cell.alignment = center_alignment
    title_cell.border = thin_border
    
    # Week selector dropdown
    worksheet.cell(row=2, column=1, value='Select Week:')
    dropdown_cell = worksheet.cell(row=2, column=2, value=max_week)  # Default to latest week
    dropdown_cell.fill = dropdown_fill
    dropdown_cell.border = thin_border
    dropdown_cell.alignment = center_alignment
    
    # Create dropdown validation with numbers only
    week_numbers = ','.join([str(i) for i in range(1, max_week + 1)])
    dv = DataValidation(type="list", formula1=f'"{week_numbers}"', allow_blank=False)
    dv.error = 'Please select a valid week number'
    dv.errorTitle = 'Invalid Week'
    worksheet.add_data_validation(dv)
    dv.add(dropdown_cell)
    
    # Headers
    worksheet.merge_cells(start_row=4, start_column=1, end_row=5, end_column=1)
    agent_header = worksheet.cell(row=4, column=1, value='Agent Name')
    agent_header.font = header_font
    agent_header.fill = header_fill
    agent_header.alignment = center_alignment
    agent_header.border = thin_border
    
    worksheet.merge_cells(start_row=4, start_column=2, end_row=5, end_column=2)
    lead_header = worksheet.cell(row=4, column=2, value='Team Lead')
    lead_header.font = header_font
    lead_header.fill = header_fill
    lead_header.alignment = center_alignment
    lead_header.border = thin_border
    
    # Create hidden data area
    data_start_row = len(weekly_data) + 10
    
    # Calculate week date ranges and store in hidden area
    week_ranges_start_row = data_start_row + max_week * (len(weekly_data) + 2) + 5
    min_date = data['Audit Date'].min()
    
    for week in range(1, max_week + 1):
        week_start = min_date + pd.Timedelta(days=(week - 1) * 7)
        week_end = week_start + pd.Timedelta(days=6)
        worksheet.cell(row=week_ranges_start_row + week - 1, column=13, value=week)
        worksheet.cell(row=week_ranges_start_row + week - 1, column=14, value=week_start.strftime('%m/%d/%Y'))
        worksheet.cell(row=week_ranges_start_row + week - 1, column=15, value=week_end.strftime('%m/%d/%Y'))
    
    # Dynamic week header with date range
    worksheet.merge_cells(start_row=4, start_column=3, end_row=4, end_column=6)
    week_header = worksheet.cell(row=4, column=3, value=f'="Week " & B2 & " (" & INDEX(N:N, {week_ranges_start_row} + B2 - 1) & " - " & INDEX(O:O, {week_ranges_start_row} + B2 - 1) & ")"')
    week_header.font = header_font
    week_header.fill = header_fill
    week_header.alignment = center_alignment
    week_header.border = thin_border
    
    # Sub-headers
    for i, sub_header in enumerate(['Audited Claims', 'Error', 'Score', 'Productivity']):
        sub_cell = worksheet.cell(row=5, column=3 + i, value=sub_header)
        sub_cell.font = header_font
        sub_cell.fill = header_fill
        sub_cell.alignment = center_alignment
        sub_cell.border = thin_border
    
    # Write all weekly data in hidden area
    for week in range(1, max_week + 1):
        week_start_row = data_start_row + (week - 1) * (len(weekly_data) + 2)
        
        # Week header in hidden area
        worksheet.cell(row=week_start_row, column=7, value=f'Week_{week}')
        
        for idx, (_, agent_data) in enumerate(weekly_data.iterrows()):
            row_num = week_start_row + idx + 1
            
            # Agent name
            worksheet.cell(row=row_num, column=7, value=agent_data['Agent Name'])
            
            # Team lead
            worksheet.cell(row=row_num, column=8, value=agent_data['Team Lead'])
            
            # Week data
            audited = agent_data[f'Week_{week}_Audited']
            errors = agent_data[f'Week_{week}_Errors']
            score = agent_data[f'Week_{week}_Score']
            productivity = agent_data[f'Week_{week}_Productivity']
            
            worksheet.cell(row=row_num, column=9, value=audited)
            worksheet.cell(row=row_num, column=10, value=errors)
            worksheet.cell(row=row_num, column=11, value=score)
            worksheet.cell(row=row_num, column=12, value=productivity)
    
    # Data rows with dynamic formulas
    row_num = 6
    for idx, (_, agent_data) in enumerate(weekly_data.iterrows()):
        # Agent name
        agent_cell = worksheet.cell(row=row_num, column=1, value=agent_data['Agent Name'])
        agent_cell.font = data_font
        agent_cell.alignment = Alignment(horizontal='left', vertical='center')
        agent_cell.border = thin_border
        
        # Team lead
        lead_cell = worksheet.cell(row=row_num, column=2, value=agent_data['Team Lead'])
        lead_cell.font = data_font
        lead_cell.alignment = center_alignment
        lead_cell.border = thin_border
        
        # Check if this is the total row
        is_total = agent_data['Agent Name'] == 'Total'
        
        # Dynamic formulas that lookup data based on selected week
        base_row = data_start_row + 1 + idx
        
        # Audited claims
        audited_formula = f'=INDEX(I{base_row}:I{base_row + max_week * (len(weekly_data) + 2)}, ($B$2-1)*{len(weekly_data) + 2}+1)'
        audited_cell = worksheet.cell(row=row_num, column=3, value=audited_formula)
        audited_cell.font = Font(bold=is_total, size=10)
        audited_cell.alignment = center_alignment
        audited_cell.border = thin_border
        if is_total:
            audited_cell.fill = total_fill
        
        # Errors
        error_formula = f'=INDEX(J{base_row}:J{base_row + max_week * (len(weekly_data) + 2)}, ($B$2-1)*{len(weekly_data) + 2}+1)'
        error_cell = worksheet.cell(row=row_num, column=4, value=error_formula)
        error_cell.font = Font(bold=is_total, size=10)
        error_cell.alignment = center_alignment
        error_cell.border = thin_border
        if is_total:
            error_cell.fill = total_fill
        
        # Score
        score_formula = f'=INDEX(K{base_row}:K{base_row + max_week * (len(weekly_data) + 2)}, ($B$2-1)*{len(weekly_data) + 2}+1)/100'
        score_cell = worksheet.cell(row=row_num, column=5, value=score_formula)
        score_cell.font = Font(bold=is_total, size=10)
        score_cell.alignment = center_alignment
        score_cell.border = thin_border
        score_cell.number_format = '0.00%'
        if is_total:
            score_cell.fill = total_fill
        
        # Productivity
        productivity_formula = f'=INDEX(L{base_row}:L{base_row + max_week * (len(weekly_data) + 2)}, ($B$2-1)*{len(weekly_data) + 2}+1)'
        productivity_cell = worksheet.cell(row=row_num, column=6, value=productivity_formula)
        productivity_cell.font = Font(bold=is_total, size=10)
        productivity_cell.alignment = center_alignment
        productivity_cell.border = thin_border
        if is_total:
            productivity_cell.fill = total_fill
        
        row_num += 1
    
    # Hide the data rows used for formulas
    for week in range(1, max_week + 1):
        week_start_row = data_start_row + (week - 1) * (len(weekly_data) + 2)
        for row_idx in range(week_start_row, week_start_row + len(weekly_data) + 2):
            worksheet.row_dimensions[row_idx].hidden = True
    
    # Set column widths
    worksheet.column_dimensions['A'].width = 20
    worksheet.column_dimensions['B'].width = 12
    worksheet.column_dimensions['C'].width = 15
    worksheet.column_dimensions['D'].width = 10
    worksheet.column_dimensions['E'].width = 12
    worksheet.column_dimensions['F'].width = 12
    
    # ========== QC SUMMARY SECTION (Dynamic based on selected week) ==========
    qc_col = None
    for col in full_data.columns:
        if 'qc' in str(col).lower():
            qc_col = col
            break

    qc_start_col = 11
    qc_start_row = 4

    worksheet.merge_cells(start_row=qc_start_row, start_column=qc_start_col, 
                         end_row=qc_start_row, end_column=qc_start_col+1)
    qc_title = worksheet.cell(row=qc_start_row, column=qc_start_col, value="QC Summary")
    qc_title.font = Font(bold=True, size=12)
    qc_title.fill = title_fill
    qc_title.alignment = center_alignment
    qc_title.border = thin_border

    # Dynamic week header for QC
    qc_week_header = worksheet.cell(row=qc_start_row+1, column=qc_start_col, value='="Week " & B2')
    qc_week_header.font = header_font
    qc_week_header.fill = dropdown_fill
    qc_week_header.alignment = center_alignment
    qc_week_header.border = thin_border
    
    # Empty cell for alignment
    worksheet.cell(row=qc_start_row+1, column=qc_start_col+1, value="").border = thin_border

    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").border = thin_border
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").border = thin_border

    if qc_col:
        # Find audit date column
        audit_date_col = None
        for col in full_data.columns:
            if col.lower().replace(' ', '').replace('_', '') == 'auditdate':
                audit_date_col = col
                break
        
        if audit_date_col:
            # Create QC data for each week and store in hidden area
            qc_data_start_row = week_ranges_start_row + max_week + 10
            
            # Add audit dates to full_data for week calculation
            full_data_copy = full_data.copy()
            full_data_copy['Audit Date'] = pd.to_datetime(full_data_copy[audit_date_col], errors='coerce')
            min_date = full_data_copy['Audit Date'].min()
            
            # Process QC data for each week
            for week in range(1, max_week + 1):
                week_start = min_date + pd.Timedelta(days=(week - 1) * 7)
                week_end = week_start + pd.Timedelta(days=6)
                
                # Filter data for this specific week
                week_mask = (full_data_copy['Audit Date'] >= week_start) & (full_data_copy['Audit Date'] <= week_end)
                week_filtered_data = full_data_copy[week_mask]
                
                if len(week_filtered_data) > 0:
                    qc_data_for_week = week_filtered_data[qc_col].dropna().astype(str)
                    qc_data_for_week = qc_data_for_week[qc_data_for_week.str.strip() != '']
                    qc_data_for_week = qc_data_for_week[qc_data_for_week != 'nan']
                    
                    if len(qc_data_for_week) > 0:
                        qc_counts_for_week = qc_data_for_week.value_counts().reset_index()
                        qc_counts_for_week.columns = ['QC Name', 'Count']
                        
                        # Store week header in hidden area
                        week_qc_start = qc_data_start_row + (week - 1) * 20
                        worksheet.cell(row=week_qc_start, column=16, value=week)
                        
                        # Store QC data for this week
                        for i, qc_row in qc_counts_for_week.iterrows():
                            worksheet.cell(row=week_qc_start + i + 1, column=16, value=str(qc_row['QC Name']))
                            worksheet.cell(row=week_qc_start + i + 1, column=17, value=int(qc_row['Count']))
            
            # Create dynamic formulas that show QC data based on selected week
            max_qc_entries = 10  # Show up to 10 QC entries
            
            for i in range(max_qc_entries):
                # QC Name formula - lookup based on selected week
                name_formula = f'=IFERROR(IF(INDEX(P{qc_data_start_row + 1}:P{qc_data_start_row + max_week * 20}, MATCH(B2, P{qc_data_start_row}:P{qc_data_start_row + max_week * 20}, 0) + {i})="","",INDEX(P{qc_data_start_row + 1}:P{qc_data_start_row + max_week * 20}, MATCH(B2, P{qc_data_start_row}:P{qc_data_start_row + max_week * 20}, 0) + {i})), "")'
                name_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col, value=name_formula)
                name_cell.font = data_font
                name_cell.border = thin_border
                name_cell.alignment = Alignment(horizontal='left', vertical='center')
                
                # Count formula - lookup based on selected week
                count_formula = f'=IFERROR(IF(INDEX(P{qc_data_start_row + 1}:P{qc_data_start_row + max_week * 20}, MATCH(B2, P{qc_data_start_row}:P{qc_data_start_row + max_week * 20}, 0) + {i})="","",INDEX(Q{qc_data_start_row + 1}:Q{qc_data_start_row + max_week * 20}, MATCH(B2, P{qc_data_start_row}:P{qc_data_start_row + max_week * 20}, 0) + {i})), "")'
                count_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col+1, value=count_formula)
                count_cell.font = data_font
                count_cell.border = thin_border
                count_cell.alignment = center_alignment
            
            # Hide the QC data columns
            worksheet.column_dimensions['P'].hidden = True
            worksheet.column_dimensions['Q'].hidden = True
            
            # Hide the QC data rows
            for week in range(1, max_week + 1):
                week_qc_start = qc_data_start_row + (week - 1) * 20
                for row_idx in range(week_qc_start, week_qc_start + 20):
                    worksheet.row_dimensions[row_idx].hidden = True
        else:
            no_audit_col_cell = worksheet.cell(row=qc_start_row+3, column=qc_start_col, value="Audit Date column not found")
            no_audit_col_cell.font = data_font
            no_audit_col_cell.border = thin_border
    else:
        no_col_cell = worksheet.cell(row=qc_start_row+3, column=qc_start_col, value="QC column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border

    # ========== SCOPE SUMMARY SECTION (Dynamic based on selected week) ==========
    scope_col = None
    for col in full_data.columns:
        if 'scope' in str(col).lower():
            scope_col = col
            break

    scope_start_col = 18  # Column R
    scope_start_row = 4

    worksheet.merge_cells(start_row=scope_start_row, start_column=scope_start_col, 
                         end_row=scope_start_row, end_column=scope_start_col+1)
    scope_title = worksheet.cell(row=scope_start_row, column=scope_start_col, value="Scope Summary")
    scope_title.font = Font(bold=True, size=12)
    scope_title.fill = title_fill
    scope_title.alignment = center_alignment
    scope_title.border = thin_border

    # Dynamic week header for Scope
    scope_week_header = worksheet.cell(row=scope_start_row+1, column=scope_start_col, value='="Week " & B2')
    scope_week_header.font = header_font
    scope_week_header.fill = dropdown_fill
    scope_week_header.alignment = center_alignment
    scope_week_header.border = thin_border
    
    # Empty cell for alignment
    worksheet.cell(row=scope_start_row+1, column=scope_start_col+1, value="").border = thin_border

    worksheet.cell(row=scope_start_row+2, column=scope_start_col, value="Scope").font = header_font
    worksheet.cell(row=scope_start_row+2, column=scope_start_col+1, value="Count").font = header_font
    worksheet.cell(row=scope_start_row+2, column=scope_start_col, value="Scope").border = thin_border
    worksheet.cell(row=scope_start_row+2, column=scope_start_col+1, value="Count").border = thin_border

    if scope_col:
        # Find audit date column
        audit_date_col = None
        for col in full_data.columns:
            if col.lower().replace(' ', '').replace('_', '') == 'auditdate':
                audit_date_col = col
                break
        
        if audit_date_col:
            # Create Scope data for each week and store in hidden area
            scope_data_start_row = qc_data_start_row + max_week * 20 + 10
            
            # Add audit dates to full_data for week calculation
            full_data_copy = full_data.copy()
            full_data_copy['Audit Date'] = pd.to_datetime(full_data_copy[audit_date_col], errors='coerce')
            min_date = full_data_copy['Audit Date'].min()
            
            # Process Scope data for each week
            for week in range(1, max_week + 1):
                week_start = min_date + pd.Timedelta(days=(week - 1) * 7)
                week_end = week_start + pd.Timedelta(days=6)
                
                # Filter data for this specific week
                week_mask = (full_data_copy['Audit Date'] >= week_start) & (full_data_copy['Audit Date'] <= week_end)
                week_filtered_data = full_data_copy[week_mask]
                
                if len(week_filtered_data) > 0:
                    # Find audited transaction column
                    audited_transaction_col = None
                    for col in week_filtered_data.columns:
                        col_lower = col.lower().replace(' ', '').replace('_', '')
                        if 'audited' in col_lower and 'transaction' in col_lower:
                            audited_transaction_col = col
                            break
                        elif col_lower == 'auditedtransaction':
                            audited_transaction_col = col
                            break
                    
                    scope_data_for_week = week_filtered_data[scope_col].dropna().astype(str)
                    scope_data_for_week_clean = scope_data_for_week[scope_data_for_week.str.strip() != '']
                    scope_data_for_week_clean = scope_data_for_week_clean[scope_data_for_week_clean != 'nan']
                    
                    if len(scope_data_for_week_clean) > 0:
                        # Group by scope and sum audited transactions instead of counting rows
                        if audited_transaction_col:
                            week_filtered_data_clean = week_filtered_data[week_filtered_data[scope_col].isin(scope_data_for_week_clean)]
                            scope_counts_for_week = week_filtered_data_clean.groupby(scope_col)[audited_transaction_col].apply(lambda x: pd.to_numeric(x, errors='coerce').fillna(1).sum()).reset_index()
                            scope_counts_for_week.columns = ['Scope', 'Count']
                            scope_counts_for_week['Count'] = scope_counts_for_week['Count'].astype(int)
                        else:
                            scope_counts_for_week = scope_data_for_week_clean.value_counts().reset_index()
                            scope_counts_for_week.columns = ['Scope', 'Count']
                        
                        # Store week header in hidden area  
                        week_scope_start = scope_data_start_row + (week - 1) * 20
                        worksheet.cell(row=week_scope_start, column=20, value=week)
                        
                        # Store Scope data for this week
                        for i, scope_row in scope_counts_for_week.iterrows():
                            worksheet.cell(row=week_scope_start + i + 1, column=20, value=str(scope_row['Scope']))
                            worksheet.cell(row=week_scope_start + i + 1, column=21, value=int(scope_row['Count']))
            
            # Create dynamic formulas that show Scope data based on selected week
            max_scope_entries = 10  # Show up to 10 Scope entries
            
            for i in range(max_scope_entries):
                # Scope Name formula - lookup based on selected week
                name_formula = f'=IFERROR(IF(INDEX(T{scope_data_start_row + 1}:T{scope_data_start_row + max_week * 20}, MATCH(B2, T{scope_data_start_row}:T{scope_data_start_row + max_week * 20}, 0) + {i})="","",INDEX(T{scope_data_start_row + 1}:T{scope_data_start_row + max_week * 20}, MATCH(B2, T{scope_data_start_row}:T{scope_data_start_row + max_week * 20}, 0) + {i})), "")'
                name_cell = worksheet.cell(row=scope_start_row+3+i, column=scope_start_col, value=name_formula)
                name_cell.font = data_font
                name_cell.border = thin_border
                name_cell.alignment = Alignment(horizontal='left', vertical='center')
                
                # Count formula - lookup based on selected week
                count_formula = f'=IFERROR(IF(INDEX(T{scope_data_start_row + 1}:T{scope_data_start_row + max_week * 20}, MATCH(B2, T{scope_data_start_row}:T{scope_data_start_row + max_week * 20}, 0) + {i})="","",INDEX(U{scope_data_start_row + 1}:U{scope_data_start_row + max_week * 20}, MATCH(B2, T{scope_data_start_row}:T{scope_data_start_row + max_week * 20}, 0) + {i})), "")'
                count_cell = worksheet.cell(row=scope_start_row+3+i, column=scope_start_col+1, value=count_formula)
                count_cell.font = data_font
                count_cell.border = thin_border
                count_cell.alignment = center_alignment
            
            # Hide the Scope data columns
            worksheet.column_dimensions['T'].hidden = True
            worksheet.column_dimensions['U'].hidden = True
            
            # Hide the Scope data rows
            for week in range(1, max_week + 1):
                week_scope_start = scope_data_start_row + (week - 1) * 20
                for row_idx in range(week_scope_start, week_scope_start + 20):
                    worksheet.row_dimensions[row_idx].hidden = True
        else:
            no_audit_col_cell = worksheet.cell(row=scope_start_row+3, column=scope_start_col, value="Audit Date column not found")
            no_audit_col_cell.font = data_font
            no_audit_col_cell.border = thin_border
    else:
        no_col_cell = worksheet.cell(row=scope_start_row+3, column=scope_start_col, value="Scope column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border

    worksheet.column_dimensions['K'].width = 15
    worksheet.column_dimensions['L'].width = 10
    worksheet.column_dimensions['R'].width = 15  # Scope Name
    worksheet.column_dimensions['S'].width = 10  # Scope Count
    
    # Hide the data columns
    worksheet.column_dimensions['G'].hidden = True
    worksheet.column_dimensions['H'].hidden = True
    worksheet.column_dimensions['I'].hidden = True
    worksheet.column_dimensions['J'].hidden = True
    worksheet.column_dimensions['M'].hidden = True
    worksheet.column_dimensions['N'].hidden = True
    worksheet.column_dimensions['O'].hidden = True

def create_monthly_summary_sheet_simple(writer, monthly_data, months, sheet_name, data, full_data):
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    
    # Get the workbook and create worksheet
    workbook = writer.book
    worksheet = workbook.create_sheet(title=sheet_name)
    
    # Define styles
    title_font = Font(bold=True, size=14)
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    total_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")
    
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Get date range
    start_date = data['Audit Date'].min().strftime('%m/%d/%Y')
    end_date = data['Audit Date'].max().strftime('%m/%d/%Y')
    
    # Title row
    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=6)
    title_cell = worksheet.cell(row=1, column=1, value=f'Monthly Audit_Charge({start_date} - {end_date})')
    title_cell.font = title_font
    title_cell.fill = title_fill
    title_cell.alignment = center_alignment
    title_cell.border = thin_border
    
    # Month selector dropdown
    worksheet.cell(row=2, column=1, value='Select Month:')
    dropdown_cell = worksheet.cell(row=2, column=2, value=months[-1])  # Default to latest month
    dropdown_cell.fill = dropdown_fill
    dropdown_cell.border = thin_border
    dropdown_cell.alignment = center_alignment
    
    # Create dropdown validation
    month_list = ','.join(months)
    dv = DataValidation(type="list", formula1=f'"{month_list}"', allow_blank=False)
    dv.error = 'Please select a valid month'
    dv.errorTitle = 'Invalid Month'
    worksheet.add_data_validation(dv)
    dv.add(dropdown_cell)
    
    # Headers
    worksheet.merge_cells(start_row=4, start_column=1, end_row=5, end_column=1)
    agent_header = worksheet.cell(row=4, column=1, value='Agent Name')
    agent_header.font = header_font
    agent_header.fill = header_fill
    agent_header.alignment = center_alignment
    agent_header.border = thin_border
    
    worksheet.merge_cells(start_row=4, start_column=2, end_row=5, end_column=2)
    lead_header = worksheet.cell(row=4, column=2, value='Team Lead')
    lead_header.font = header_font
    lead_header.fill = header_fill
    lead_header.alignment = center_alignment
    lead_header.border = thin_border
    
    # Dynamic month header
    worksheet.merge_cells(start_row=4, start_column=3, end_row=4, end_column=6)
    month_header = worksheet.cell(row=4, column=3, value='=B2')
    month_header.font = header_font
    month_header.fill = header_fill
    month_header.alignment = center_alignment
    month_header.border = thin_border
    
    # Sub-headers
    for i, sub_header in enumerate(['Audited Claims', 'Error', 'Score', 'Productivity']):
        sub_cell = worksheet.cell(row=5, column=3 + i, value=sub_header)
        sub_cell.font = header_font
        sub_cell.fill = header_fill
        sub_cell.alignment = center_alignment
        sub_cell.border = thin_border
    
    # Create hidden data area
    data_start_row = len(monthly_data) + 10
    
    # Write all monthly data in hidden area with improved structure
    for month_idx, month in enumerate(months):
        # Each month gets a block of rows: header + all agents
        month_block_start = data_start_row + month_idx * (len(monthly_data) + 2)
        
        # Month header in hidden area
        worksheet.cell(row=month_block_start, column=7, value=month)
        
        # Write data for each agent in this month
        for agent_idx, (_, agent_data) in enumerate(monthly_data.iterrows()):
            data_row = month_block_start + agent_idx + 1
            
            # Agent name and team lead for reference
            worksheet.cell(row=data_row, column=7, value=agent_data['Agent Name'])
            worksheet.cell(row=data_row, column=8, value=agent_data['Team Lead'])
            
            # Month data for this agent
            audited = agent_data[f'{month}_Audited']
            errors = agent_data[f'{month}_Errors']
            score = agent_data[f'{month}_Score']
            productivity = agent_data[f'{month}_Productivity']
            
            worksheet.cell(row=data_row, column=9, value=audited)
            worksheet.cell(row=data_row, column=10, value=errors)
            worksheet.cell(row=data_row, column=11, value=score)
            worksheet.cell(row=data_row, column=12, value=productivity)
    
    # Data rows with improved dynamic formulas
    row_num = 6
    for idx, (_, agent_data) in enumerate(monthly_data.iterrows()):
        # Agent name
        agent_cell = worksheet.cell(row=row_num, column=1, value=agent_data['Agent Name'])
        agent_cell.font = data_font
        agent_cell.alignment = Alignment(horizontal='left', vertical='center')
        agent_cell.border = thin_border
        
        # Team lead
        lead_cell = worksheet.cell(row=row_num, column=2, value=agent_data['Team Lead'])
        lead_cell.font = data_font
        lead_cell.alignment = center_alignment
        lead_cell.border = thin_border
        
        # Check if this is the total row
        is_total = agent_data['Agent Name'] == 'Total'
        
        # Calculate the correct row offset for each month block
        # Each month block has: header row (1) + all agent rows (len(monthly_data))
        block_size = len(monthly_data) + 2
        
        if is_total:
            # For total row, sum all individual agent rows (excluding the total row itself)
            sum_range = f'C6:C{5 + len(monthly_data) - 1}'  # Exclude the total row
            audited_formula = f'=SUM({sum_range})'
        else:
            # For individual agents, find the specific row
            agent_offset = idx + 1  # +1 because of header row in each block
            audited_formula = f'=IFERROR(INDEX(I{data_start_row}:I{data_start_row + len(months) * block_size}, MATCH(B2, G{data_start_row}:G{data_start_row + len(months) * block_size}, 0) + {agent_offset}), 0)'
        
        audited_cell = worksheet.cell(row=row_num, column=3, value=audited_formula)
        audited_cell.font = Font(bold=is_total, size=10)
        audited_cell.alignment = center_alignment
        audited_cell.border = thin_border
        if is_total:
            audited_cell.fill = total_fill
        
        # Similar formulas for errors
        if is_total:
            # For total row, sum all individual agent rows (excluding the total row itself)
            sum_range = f'D6:D{5 + len(monthly_data) - 1}'  # Exclude the total row
            error_formula = f'=SUM({sum_range})'
        else:
            error_formula = f'=IFERROR(INDEX(J{data_start_row}:J{data_start_row + len(months) * block_size}, MATCH(B2, G{data_start_row}:G{data_start_row + len(months) * block_size}, 0) + {agent_offset}), 0)'
        
        error_cell = worksheet.cell(row=row_num, column=4, value=error_formula)
        error_cell.font = Font(bold=is_total, size=10)
        error_cell.alignment = center_alignment
        error_cell.border = thin_border
        if is_total:
            error_cell.fill = total_fill
        
        # For score, calculate dynamically for total
        if is_total:
            score_formula = f'=IF(C{row_num}>0, (C{row_num}-D{row_num})/C{row_num}, 1)'
        else:
            score_formula = f'=IFERROR(INDEX(K{data_start_row}:K{data_start_row + len(months) * block_size}, MATCH(B2, G{data_start_row}:G{data_start_row + len(months) * block_size}, 0) + {agent_offset})/100, 1)'
        
        score_cell = worksheet.cell(row=row_num, column=5, value=score_formula)
        score_cell.font = Font(bold=is_total, size=10)
        score_cell.alignment = center_alignment
        score_cell.border = thin_border
        score_cell.number_format = '0.00%'
        if is_total:
            score_cell.fill = total_fill
        
        # FIXED: Productivity formula - much simpler approach
        if is_total:
            # For total row, sum all individual agent rows (excluding the total row itself)
            sum_range = f'F6:F{5 + len(monthly_data) - 1}'  # Exclude the total row
            productivity_formula = f'=SUM({sum_range})'
        else:
            productivity_formula = f'=IFERROR(INDEX(L{data_start_row}:L{data_start_row + len(months) * block_size}, MATCH(B2, G{data_start_row}:G{data_start_row + len(months) * block_size}, 0) + {agent_offset}), 0)'
        
        productivity_cell = worksheet.cell(row=row_num, column=6, value=productivity_formula)
        productivity_cell.font = Font(bold=is_total, size=10)
        productivity_cell.alignment = center_alignment
        productivity_cell.border = thin_border
        if is_total:
            productivity_cell.fill = total_fill
        
        row_num += 1
    
    # Hide the data rows used for formulas
    for month_idx in range(len(months)):
        month_start_row = data_start_row + month_idx * (len(monthly_data) + 2)
        for row_idx in range(month_start_row, month_start_row + len(monthly_data) + 2):
            worksheet.row_dimensions[row_idx].hidden = True
    
    # Set column widths
    worksheet.column_dimensions['A'].width = 20
    worksheet.column_dimensions['B'].width = 12
    worksheet.column_dimensions['C'].width = 15
    worksheet.column_dimensions['D'].width = 10
    worksheet.column_dimensions['E'].width = 12
    worksheet.column_dimensions['F'].width = 12
    
    # ========== QC SUMMARY SECTION (Dynamic based on selected month) ==========
    qc_col = None
    for col in full_data.columns:
        if 'qc' in str(col).lower():
            qc_col = col
            break

    qc_start_col = 11
    qc_start_row = 4

    worksheet.merge_cells(start_row=qc_start_row, start_column=qc_start_col, 
                         end_row=qc_start_row, end_column=qc_start_col+1)
    qc_title = worksheet.cell(row=qc_start_row, column=qc_start_col, value="QC Summary")
    qc_title.font = Font(bold=True, size=12)
    qc_title.fill = title_fill
    qc_title.alignment = center_alignment
    qc_title.border = thin_border

    # Dynamic month header for QC
    qc_month_header = worksheet.cell(row=qc_start_row+1, column=qc_start_col, value='=B2')
    qc_month_header.font = header_font
    qc_month_header.fill = dropdown_fill
    qc_month_header.alignment = center_alignment
    qc_month_header.border = thin_border
    
    # Empty cell for alignment
    worksheet.cell(row=qc_start_row+1, column=qc_start_col+1, value="").border = thin_border

    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").border = thin_border
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").border = thin_border

    if qc_col:
        # Find audit date column
        audit_date_col = None
        for col in full_data.columns:
            if col.lower().replace(' ', '').replace('_', '') == 'auditdate':
                audit_date_col = col
                break
        
        if audit_date_col:
            # Create QC data for each month and store in hidden area
            qc_data_start_row = data_start_row + len(months) * (len(monthly_data) + 2) + 10
            
            # Add audit dates to full_data for month calculation
            full_data_copy = full_data.copy()
            full_data_copy['Audit Date'] = pd.to_datetime(full_data_copy[audit_date_col], errors='coerce')
            full_data_copy['Month_Year'] = full_data_copy['Audit Date'].dt.to_period('M').astype(str)
            
            # Process QC data for each month
            for month_idx, month in enumerate(months):
                # Filter data for this specific month
                month_filtered_data = full_data_copy[full_data_copy['Month_Year'] == month]
                
                if len(month_filtered_data) > 0:
                    qc_data_for_month = month_filtered_data[qc_col].dropna().astype(str)
                    qc_data_for_month = qc_data_for_month[qc_data_for_month.str.strip() != '']
                    qc_data_for_month = qc_data_for_month[qc_data_for_month != 'nan']
                    
                    if len(qc_data_for_month) > 0:
                        qc_counts_for_month = qc_data_for_month.value_counts().reset_index()
                        qc_counts_for_month.columns = ['QC Name', 'Count']
                        
                        # Store month header in hidden area
                        month_qc_start = qc_data_start_row + month_idx * 20
                        worksheet.cell(row=month_qc_start, column=16, value=month)
                        
                        # Store QC data for this month
                        for i, qc_row in qc_counts_for_month.iterrows():
                            worksheet.cell(row=month_qc_start + i + 1, column=16, value=str(qc_row['QC Name']))
                            worksheet.cell(row=month_qc_start + i + 1, column=17, value=int(qc_row['Count']))
            
            # Create dynamic formulas that show QC data based on selected month
            max_qc_entries = 10  # Show up to 10 QC entries
            
            for i in range(max_qc_entries):
                # QC Name formula - lookup based on selected month
                name_formula = f'=IFERROR(IF(INDEX(P{qc_data_start_row + 1}:P{qc_data_start_row + len(months) * 20}, MATCH(B2, P{qc_data_start_row}:P{qc_data_start_row + len(months) * 20}, 0) + {i})="","",INDEX(P{qc_data_start_row + 1}:P{qc_data_start_row + len(months) * 20}, MATCH(B2, P{qc_data_start_row}:P{qc_data_start_row + len(months) * 20}, 0) + {i})), "")'
                name_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col, value=name_formula)
                name_cell.font = data_font
                name_cell.border = thin_border
                name_cell.alignment = Alignment(horizontal='left', vertical='center')
                
                # Count formula - lookup based on selected month
                count_formula = f'=IFERROR(IF(INDEX(P{qc_data_start_row + 1}:P{qc_data_start_row + len(months) * 20}, MATCH(B2, P{qc_data_start_row}:P{qc_data_start_row + len(months) * 20}, 0) + {i})="","",INDEX(Q{qc_data_start_row + 1}:Q{qc_data_start_row + len(months) * 20}, MATCH(B2, P{qc_data_start_row}:P{qc_data_start_row + len(months) * 20}, 0) + {i})), "")'
                count_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col+1, value=count_formula)
                count_cell.font = data_font
                count_cell.border = thin_border
                count_cell.alignment = center_alignment
            
            # Hide the QC data columns
            worksheet.column_dimensions['P'].hidden = True
            worksheet.column_dimensions['Q'].hidden = True
            
            # Hide the QC data rows
            for month_idx in range(len(months)):
                month_qc_start = qc_data_start_row + month_idx * 20
                for row_idx in range(month_qc_start, month_qc_start + 20):
                    worksheet.row_dimensions[row_idx].hidden = True
        else:
            no_audit_col_cell = worksheet.cell(row=qc_start_row+3, column=qc_start_col, value="Audit Date column not found")
            no_audit_col_cell.font = data_font
            no_audit_col_cell.border = thin_border
    else:
        no_col_cell = worksheet.cell(row=qc_start_row+3, column=qc_start_col, value="QC column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border

    # ========== SCOPE SUMMARY SECTION (Dynamic based on selected month) ==========
    scope_col = None
    for col in full_data.columns:
        if 'scope' in str(col).lower():
            scope_col = col
            break

    scope_start_col = 18  # Column R
    scope_start_row = 4

    worksheet.merge_cells(start_row=scope_start_row, start_column=scope_start_col, 
                         end_row=scope_start_row, end_column=scope_start_col+1)
    scope_title = worksheet.cell(row=scope_start_row, column=scope_start_col, value="Scope Summary")
    scope_title.font = Font(bold=True, size=12)
    scope_title.fill = title_fill
    scope_title.alignment = center_alignment
    scope_title.border = thin_border

    # Dynamic month header for Scope
    scope_month_header = worksheet.cell(row=scope_start_row+1, column=scope_start_col, value='=B2')
    scope_month_header.font = header_font
    scope_month_header.fill = dropdown_fill
    scope_month_header.alignment = center_alignment
    scope_month_header.border = thin_border
    
    # Empty cell for alignment
    worksheet.cell(row=scope_start_row+1, column=scope_start_col+1, value="").border = thin_border

    worksheet.cell(row=scope_start_row+2, column=scope_start_col, value="Scope").font = header_font
    worksheet.cell(row=scope_start_row+2, column=scope_start_col+1, value="Count").font = header_font
    worksheet.cell(row=scope_start_row+2, column=scope_start_col, value="Scope").border = thin_border
    worksheet.cell(row=scope_start_row+2, column=scope_start_col+1, value="Count").border = thin_border

    if scope_col:
        # Find audit date column
        audit_date_col = None
        for col in full_data.columns:
            if col.lower().replace(' ', '').replace('_', '') == 'auditdate':
                audit_date_col = col
                break
        
        if audit_date_col:
            # Create Scope data for each month and store in hidden area
            scope_data_start_row = qc_data_start_row + len(months) * 20 + 10
            
            # Add audit dates to full_data for month calculation
            full_data_copy = full_data.copy()
            full_data_copy['Audit Date'] = pd.to_datetime(full_data_copy[audit_date_col], errors='coerce')
            full_data_copy['Month_Year'] = full_data_copy['Audit Date'].dt.to_period('M').astype(str)
            
            # Process Scope data for each month
            for month_idx, month in enumerate(months):
                # Filter data for this specific month
                month_filtered_data = full_data_copy[full_data_copy['Month_Year'] == month]
                
                if len(month_filtered_data) > 0:
                    # Find audited transaction column
                    audited_transaction_col = None
                    for col in month_filtered_data.columns:
                        col_lower = col.lower().replace(' ', '').replace('_', '')
                        if 'audited' in col_lower and 'transaction' in col_lower:
                            audited_transaction_col = col
                            break
                        elif col_lower == 'auditedtransaction':
                            audited_transaction_col = col
                            break
                    
                    scope_data_for_month = month_filtered_data[scope_col].dropna().astype(str)
                    scope_data_for_month_clean = scope_data_for_month[scope_data_for_month.str.strip() != '']
                    scope_data_for_month_clean = scope_data_for_month_clean[scope_data_for_month_clean != 'nan']
                    
                    if len(scope_data_for_month_clean) > 0:
                        # Group by scope and sum audited transactions instead of counting rows
                        if audited_transaction_col:
                            month_filtered_data_clean = month_filtered_data[month_filtered_data[scope_col].isin(scope_data_for_month_clean)]
                            scope_counts_for_month = month_filtered_data_clean.groupby(scope_col)[audited_transaction_col].apply(lambda x: pd.to_numeric(x, errors='coerce').fillna(1).sum()).reset_index()
                            scope_counts_for_month.columns = ['Scope', 'Count']
                            scope_counts_for_month['Count'] = scope_counts_for_month['Count'].astype(int)
                        else:
                            scope_counts_for_month = scope_data_for_month_clean.value_counts().reset_index()
                            scope_counts_for_month.columns = ['Scope', 'Count']
                        
                        # Store month header in hidden area
                        month_scope_start = scope_data_start_row + month_idx * 20
                        worksheet.cell(row=month_scope_start, column=20, value=month)
                        
                        # Store Scope data for this month
                        for i, scope_row in scope_counts_for_month.iterrows():
                            worksheet.cell(row=month_scope_start + i + 1, column=20, value=str(scope_row['Scope']))
                            worksheet.cell(row=month_scope_start + i + 1, column=21, value=int(scope_row['Count']))
            
            # Create dynamic formulas that show Scope data based on selected month
            max_scope_entries = 10  # Show up to 10 Scope entries
            
            for i in range(max_scope_entries):
                # Scope Name formula - lookup based on selected month
                name_formula = f'=IFERROR(IF(INDEX(T{scope_data_start_row + 1}:T{scope_data_start_row + len(months) * 20}, MATCH(B2, T{scope_data_start_row}:T{scope_data_start_row + len(months) * 20}, 0) + {i})="","",INDEX(T{scope_data_start_row + 1}:T{scope_data_start_row + len(months) * 20}, MATCH(B2, T{scope_data_start_row}:T{scope_data_start_row + len(months) * 20}, 0) + {i})), "")'
                name_cell = worksheet.cell(row=scope_start_row+3+i, column=scope_start_col, value=name_formula)
                name_cell.font = data_font
                name_cell.border = thin_border
                name_cell.alignment = Alignment(horizontal='left', vertical='center')
                
                # Count formula - lookup based on selected month
                count_formula = f'=IFERROR(IF(INDEX(T{scope_data_start_row + 1}:T{scope_data_start_row + len(months) * 20}, MATCH(B2, T{scope_data_start_row}:T{scope_data_start_row + len(months) * 20}, 0) + {i})="","",INDEX(U{scope_data_start_row + 1}:U{scope_data_start_row + len(months) * 20}, MATCH(B2, T{scope_data_start_row}:T{scope_data_start_row + len(months) * 20}, 0) + {i})), "")'
                count_cell = worksheet.cell(row=scope_start_row+3+i, column=scope_start_col+1, value=count_formula)
                count_cell.font = data_font
                count_cell.border = thin_border
                count_cell.alignment = center_alignment
            
            # Hide the Scope data columns
            worksheet.column_dimensions['T'].hidden = True
            worksheet.column_dimensions['U'].hidden = True
            
            # Hide the Scope data rows
            for month_idx in range(len(months)):
                month_scope_start = scope_data_start_row + month_idx * 20
                for row_idx in range(month_scope_start, month_scope_start + 20):
                    worksheet.row_dimensions[row_idx].hidden = True
        else:
            no_audit_col_cell = worksheet.cell(row=scope_start_row+3, column=scope_start_col, value="Audit Date column not found")
            no_audit_col_cell.font = data_font
            no_audit_col_cell.border = thin_border
    else:
        no_col_cell = worksheet.cell(row=scope_start_row+3, column=scope_start_col, value="Scope column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border

    worksheet.column_dimensions['K'].width = 15
    worksheet.column_dimensions['L'].width = 10
    worksheet.column_dimensions['R'].width = 15  # Scope Name
    worksheet.column_dimensions['S'].width = 10  # Scope Count
    
    # Hide the data columns
    worksheet.column_dimensions['G'].hidden = True
    worksheet.column_dimensions['H'].hidden = True
    worksheet.column_dimensions['I'].hidden = True
    worksheet.column_dimensions['J'].hidden = True
    worksheet.column_dimensions['M'].hidden = True

# def create_daily_summary_sheet_simple(writer, daily_data, unique_dates, sheet_name, data):
def create_daily_summary_sheet_simple(writer, daily_data, unique_dates, sheet_name, data, full_data):
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.worksheet.datavalidation import DataValidation
    
    # Get the workbook and create worksheet
    workbook = writer.book
    worksheet = workbook.create_sheet(title=sheet_name)
    
    # Define styles
    title_font = Font(bold=True, size=14)
    header_font = Font(bold=True, size=11)
    data_font = Font(size=10)
    
    title_fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
    header_fill = PatternFill(start_color="D7E4BC", end_color="D7E4BC", fill_type="solid")
    dropdown_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
    total_fill = PatternFill(start_color="FFE6CC", end_color="FFE6CC", fill_type="solid")
    
    thin_border = Border(
        left=Side(style='thin'), right=Side(style='thin'),
        top=Side(style='thin'), bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    left_alignment = Alignment(horizontal='left', vertical='center')
    
    # Get date range
    start_date = data['Audit Date'].min().strftime('%m/%d/%Y')
    end_date = data['Audit Date'].max().strftime('%m/%d/%Y')
    
    # Title row
    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=7)
    title_cell = worksheet.cell(row=1, column=1, value=f'Daily Audit_Charge({start_date} - {end_date})')
    title_cell.font = title_font
    title_cell.fill = title_fill
    title_cell.alignment = center_alignment
    title_cell.border = thin_border
    
    # Date selector dropdown
    worksheet.cell(row=2, column=1, value='Select Date:')
    dropdown_cell = worksheet.cell(row=2, column=2, value=unique_dates[-1].strftime('%Y-%m-%d'))
    dropdown_cell.fill = dropdown_fill
    dropdown_cell.border = thin_border
    dropdown_cell.alignment = center_alignment
    
    # Create dropdown validation with proper range
    date_range_start = len(daily_data) + 20
    for i, date in enumerate(unique_dates):
        worksheet.cell(row=date_range_start + i, column=13, value=date.strftime('%Y-%m-%d'))
    
    date_range = f'M{date_range_start}:M{date_range_start + len(unique_dates) - 1}'
    dv = DataValidation(type="list", formula1=date_range, allow_blank=False)
    dv.error = 'Please select a valid date'
    dv.errorTitle = 'Invalid Date'
    worksheet.add_data_validation(dv)
    dv.add(dropdown_cell)
    
    # Hide the date list column
    worksheet.column_dimensions['M'].hidden = True
    
    # Headers
    worksheet.merge_cells(start_row=4, start_column=1, end_row=5, end_column=1)
    agent_header = worksheet.cell(row=4, column=1, value='Agent Name')
    agent_header.font = header_font
    agent_header.fill = header_fill
    agent_header.alignment = center_alignment
    agent_header.border = thin_border
    
    worksheet.merge_cells(start_row=4, start_column=2, end_row=5, end_column=2)
    lead_header = worksheet.cell(row=4, column=2, value='Team Lead')
    lead_header.font = header_font
    lead_header.fill = header_fill
    lead_header.alignment = center_alignment
    lead_header.border = thin_border
    
    # Dynamic date header
    worksheet.merge_cells(start_row=4, start_column=3, end_row=4, end_column=7)
    date_header = worksheet.cell(row=4, column=3, value='=B2')
    date_header.font = header_font
    date_header.fill = header_fill
    date_header.alignment = center_alignment
    date_header.border = thin_border
    
    # Sub-headers
    for i, sub_header in enumerate(['Audited Claims', 'Error', 'Score', 'Productivity', 'Productivity %']):
        sub_cell = worksheet.cell(row=5, column=3 + i, value=sub_header)
        sub_cell.font = header_font
        sub_cell.fill = header_fill
        sub_cell.alignment = center_alignment
        sub_cell.border = thin_border
    
    # Create hidden data area
    data_start_row = len(daily_data) + 10
    
    # Write all daily data in hidden area with proper structure
    for date_idx, date in enumerate(unique_dates):
        date_str = date.strftime('%Y-%m-%d')
        date_header_row = data_start_row + date_idx * (len(daily_data) + 1)
        
        # Date header in hidden area
        worksheet.cell(row=date_header_row, column=7, value=date_str)
        
        for idx, (_, agent_data) in enumerate(daily_data.iterrows()):
            data_row = date_header_row + idx + 1
            
            # Daily data for this date and agent
            audited = agent_data[f'{date_str}_Audited']
            errors = agent_data[f'{date_str}_Errors']
            score = agent_data[f'{date_str}_Score']
            productivity = agent_data[f'{date_str}_Productivity']
            
            worksheet.cell(row=data_row, column=9, value=audited)
            worksheet.cell(row=data_row, column=10, value=errors)
            worksheet.cell(row=data_row, column=11, value=score)
            worksheet.cell(row=data_row, column=12, value=productivity)
    
    # Data rows with dynamic formulas
    row_num = 6
    for idx, (_, agent_data) in enumerate(daily_data.iterrows()):
        # Agent name
        agent_cell = worksheet.cell(row=row_num, column=1, value=agent_data['Agent Name'])
        agent_cell.font = data_font
        agent_cell.alignment = left_alignment
        agent_cell.border = thin_border
        
        # Team lead
        lead_cell = worksheet.cell(row=row_num, column=2, value=agent_data['Team Lead'])
        lead_cell.font = data_font
        lead_cell.alignment = center_alignment
        lead_cell.border = thin_border
        
        # Check if this is the total row
        is_total = agent_data['Agent Name'] == 'Total'
        
        # Audited claims
        audited_formula = f'=IFERROR(INDEX(I:I, MATCH(B2, G:G, 0) + {idx + 1}), 0)'
        audited_cell = worksheet.cell(row=row_num, column=3, value=audited_formula)
        audited_cell.font = Font(bold=is_total, size=10)
        audited_cell.alignment = center_alignment
        audited_cell.border = thin_border
        if is_total:
            audited_cell.fill = total_fill
        
        # Errors
        error_formula = f'=IFERROR(INDEX(J:J, MATCH(B2, G:G, 0) + {idx + 1}), 0)'
        error_cell = worksheet.cell(row=row_num, column=4, value=error_formula)
        error_cell.font = Font(bold=is_total, size=10)
        error_cell.alignment = center_alignment
        error_cell.border = thin_border
        if is_total:
            error_cell.fill = total_fill
        
        # Score
        score_formula = f'=IFERROR(INDEX(K:K, MATCH(B2, G:G, 0) + {idx + 1})/100, 1)'
        score_cell = worksheet.cell(row=row_num, column=5, value=score_formula)
        score_cell.font = Font(bold=is_total, size=10)
        score_cell.alignment = center_alignment
        score_cell.border = thin_border
        score_cell.number_format = '0.00%'
        if is_total:
            score_cell.fill = total_fill
        
        # Productivity
        productivity_formula = f'=IFERROR(INDEX(L:L, MATCH(B2, G:G, 0) + {idx + 1}), 0)'
        productivity_cell = worksheet.cell(row=row_num, column=6, value=productivity_formula)
        productivity_cell.font = Font(bold=is_total, size=10)
        productivity_cell.alignment = center_alignment
        productivity_cell.border = thin_border
        if is_total:
            productivity_cell.fill = total_fill
        
        row_num += 1
    
    # Hide the data rows used for formulas
    for date_idx in range(len(unique_dates)):
        date_header_row = data_start_row + date_idx * (len(daily_data) + 1)
        for row_idx in range(date_header_row, date_header_row + len(daily_data) + 1):
            worksheet.row_dimensions[row_idx].hidden = True

    # ========== QC SUMMARY SECTION (Dynamic based on selected date) ==========
    qc_col = None
    for col in full_data.columns:
        if 'qc' in str(col).lower():
            qc_col = col
            break

    # Position QC summary 4 columns to the right of main table (column K)
    qc_start_col = 11  # Column K (A=1, B=2, ..., K=11)
    qc_start_row = 4   # Start at same row as headers

    # QC Summary Title
    worksheet.merge_cells(start_row=qc_start_row, start_column=qc_start_col, 
                         end_row=qc_start_row, end_column=qc_start_col+1)
    qc_title = worksheet.cell(row=qc_start_row, column=qc_start_col, value="QC Summary")
    qc_title.font = Font(bold=True, size=12)
    qc_title.fill = title_fill
    qc_title.alignment = center_alignment
    qc_title.border = thin_border

    # Dynamic date header for QC
    qc_date_header = worksheet.cell(row=qc_start_row+1, column=qc_start_col, value='=B2')
    qc_date_header.font = header_font
    qc_date_header.fill = dropdown_fill
    qc_date_header.alignment = center_alignment
    qc_date_header.border = thin_border
    
    # Empty cell for alignment
    worksheet.cell(row=qc_start_row+1, column=qc_start_col+1, value="").border = thin_border

    # QC Headers
    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").font = header_font
    worksheet.cell(row=qc_start_row+2, column=qc_start_col, value="QC Name").border = thin_border
    worksheet.cell(row=qc_start_row+2, column=qc_start_col+1, value="Count").border = thin_border

    if qc_col:
        # Find audit date column
        audit_date_col = None
        for col in full_data.columns:
            if col.lower().replace(' ', '').replace('_', '') == 'auditdate':
                audit_date_col = col
                break
        
        if audit_date_col:
            # Create QC data for each date and store in hidden area
            qc_data_start_row = data_start_row + len(unique_dates) * (len(daily_data) + 1) + 10
            
            # Process QC data for each unique date
            for date_idx, date in enumerate(unique_dates):
                date_str = date.strftime('%Y-%m-%d')
                
                # Filter data for this specific date
                date_mask = pd.to_datetime(full_data[audit_date_col], errors='coerce').dt.date == date
                date_filtered_data = full_data[date_mask]
                
                if len(date_filtered_data) > 0:
                    qc_data_for_date = date_filtered_data[qc_col].dropna().astype(str)
                    qc_data_for_date = qc_data_for_date[qc_data_for_date.str.strip() != '']
                    qc_data_for_date = qc_data_for_date[qc_data_for_date != 'nan']
                    
                    if len(qc_data_for_date) > 0:
                        qc_counts_for_date = qc_data_for_date.value_counts().reset_index()
                        qc_counts_for_date.columns = ['QC Name', 'Count']
                        
                        # Store date header in hidden area
                        date_qc_start = qc_data_start_row + date_idx * 20
                        worksheet.cell(row=date_qc_start, column=14, value=date_str)
                        
                        # Store QC data for this date
                        for i, qc_row in qc_counts_for_date.iterrows():
                            worksheet.cell(row=date_qc_start + i + 1, column=14, value=str(qc_row['QC Name']))
                            worksheet.cell(row=date_qc_start + i + 1, column=15, value=int(qc_row['Count']))
            
            # Create dynamic formulas that show QC data based on selected date
            max_qc_entries = 10  # Show up to 10 QC entries
            
            for i in range(max_qc_entries):
                # QC Name formula - lookup based on selected date
                name_formula = f'=IFERROR(IF(INDEX(N{qc_data_start_row + 1}:N{qc_data_start_row + len(unique_dates) * 20}, MATCH(B2, N{qc_data_start_row}:N{qc_data_start_row + len(unique_dates) * 20}, 0) + {i})="","",INDEX(N{qc_data_start_row + 1}:N{qc_data_start_row + len(unique_dates) * 20}, MATCH(B2, N{qc_data_start_row}:N{qc_data_start_row + len(unique_dates) * 20}, 0) + {i})), "")'
                name_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col, value=name_formula)
                name_cell.font = data_font
                name_cell.border = thin_border
                name_cell.alignment = left_alignment
                
                # Count formula - lookup based on selected date
                count_formula = f'=IFERROR(IF(INDEX(N{qc_data_start_row + 1}:N{qc_data_start_row + len(unique_dates) * 20}, MATCH(B2, N{qc_data_start_row}:N{qc_data_start_row + len(unique_dates) * 20}, 0) + {i})="","",INDEX(O{qc_data_start_row + 1}:O{qc_data_start_row + len(unique_dates) * 20}, MATCH(B2, N{qc_data_start_row}:N{qc_data_start_row + len(unique_dates) * 20}, 0) + {i})), "")'
                count_cell = worksheet.cell(row=qc_start_row+3+i, column=qc_start_col+1, value=count_formula)
                count_cell.font = data_font
                count_cell.border = thin_border
                count_cell.alignment = center_alignment
            
            # Hide the QC data columns
            worksheet.column_dimensions['N'].hidden = True
            worksheet.column_dimensions['O'].hidden = True
            
            # Hide the QC data rows
            for date_idx in range(len(unique_dates)):
                date_qc_start = qc_data_start_row + date_idx * 20
                for row_idx in range(date_qc_start, date_qc_start + 20):
                    worksheet.row_dimensions[row_idx].hidden = True
        else:
            no_audit_col_cell = worksheet.cell(row=qc_start_row+3, column=qc_start_col, value="Audit Date column not found")
            no_audit_col_cell.font = data_font
            no_audit_col_cell.border = thin_border
    else:
        no_col_cell = worksheet.cell(row=qc_start_row+3, column=qc_start_col, value="QC column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border

    # ========== SCOPE SUMMARY SECTION (Dynamic based on selected date) ==========
    scope_col = None
    for col in full_data.columns:
        if 'scope' in str(col).lower():
            scope_col = col
            break

    # Position Scope summary next to QC summary (column N)
    scope_start_col = 16  # Column P (A=1, B=2, ..., P=16)
    scope_start_row = 4   # Start at same row as headers

    # Scope Summary Title
    worksheet.merge_cells(start_row=scope_start_row, start_column=scope_start_col, 
                         end_row=scope_start_row, end_column=scope_start_col+1)
    scope_title = worksheet.cell(row=scope_start_row, column=scope_start_col, value="Scope Summary")
    scope_title.font = Font(bold=True, size=12)
    scope_title.fill = title_fill
    scope_title.alignment = center_alignment
    scope_title.border = thin_border

    # Dynamic date header for Scope
    scope_date_header = worksheet.cell(row=scope_start_row+1, column=scope_start_col, value='=B2')
    scope_date_header.font = header_font
    scope_date_header.fill = dropdown_fill
    scope_date_header.alignment = center_alignment
    scope_date_header.border = thin_border
    
    # Empty cell for alignment
    worksheet.cell(row=scope_start_row+1, column=scope_start_col+1, value="").border = thin_border

    # Scope Headers
    worksheet.cell(row=scope_start_row+2, column=scope_start_col, value="Scope").font = header_font
    worksheet.cell(row=scope_start_row+2, column=scope_start_col+1, value="Count").font = header_font
    worksheet.cell(row=scope_start_row+2, column=scope_start_col, value="Scope").border = thin_border
    worksheet.cell(row=scope_start_row+2, column=scope_start_col+1, value="Count").border = thin_border

    if scope_col:
        # Find audit date column
        audit_date_col = None
        for col in full_data.columns:
            if col.lower().replace(' ', '').replace('_', '') == 'auditdate':
                audit_date_col = col
                break
        
        if audit_date_col:
            # Create Scope data for each date and store in hidden area
            scope_data_start_row = qc_data_start_row + len(unique_dates) * 20 + 10
            
            # Process Scope data for each unique date
            for date_idx, date in enumerate(unique_dates):
                date_str = date.strftime('%Y-%m-%d')
                
                # Filter data for this specific date
                date_mask = pd.to_datetime(full_data[audit_date_col], errors='coerce').dt.date == date
                date_filtered_data = full_data[date_mask]
                
                if len(date_filtered_data) > 0:
                    # Find audited transaction column
                    audited_transaction_col = None
                    for col in date_filtered_data.columns:
                        col_lower = col.lower().replace(' ', '').replace('_', '')
                        if 'audited' in col_lower and 'transaction' in col_lower:
                            audited_transaction_col = col
                            break
                        elif col_lower == 'auditedtransaction':
                            audited_transaction_col = col
                            break
                    
                    scope_data_for_date = date_filtered_data[scope_col].dropna().astype(str)
                    scope_data_for_date_clean = scope_data_for_date[scope_data_for_date.str.strip() != '']
                    scope_data_for_date_clean = scope_data_for_date_clean[scope_data_for_date_clean != 'nan']
                    
                    if len(scope_data_for_date_clean) > 0:
                        # Group by scope and sum audited transactions instead of counting rows
                        if audited_transaction_col:
                            date_filtered_data_clean = date_filtered_data[date_filtered_data[scope_col].isin(scope_data_for_date_clean)]
                            scope_counts_for_date = date_filtered_data_clean.groupby(scope_col)[audited_transaction_col].apply(lambda x: pd.to_numeric(x, errors='coerce').fillna(1).sum()).reset_index()
                            scope_counts_for_date.columns = ['Scope', 'Count']
                            scope_counts_for_date['Count'] = scope_counts_for_date['Count'].astype(int)
                        else:
                            scope_counts_for_date = scope_data_for_date_clean.value_counts().reset_index()
                            scope_counts_for_date.columns = ['Scope', 'Count']
                        
                        # Store date header in hidden area
                        date_scope_start = scope_data_start_row + date_idx * 20
                        worksheet.cell(row=date_scope_start, column=18, value=date_str)
                        
                        # Store Scope data for this date
                        for i, scope_row in scope_counts_for_date.iterrows():
                            worksheet.cell(row=date_scope_start + i + 1, column=18, value=str(scope_row['Scope']))
                            worksheet.cell(row=date_scope_start + i + 1, column=19, value=int(scope_row['Count']))
            
            # Create dynamic formulas that show Scope data based on selected date
            max_scope_entries = 10  # Show up to 10 Scope entries
            
            for i in range(max_scope_entries):
                # Scope Name formula - lookup based on selected date
                name_formula = f'=IFERROR(IF(INDEX(R{scope_data_start_row + 1}:R{scope_data_start_row + len(unique_dates) * 20}, MATCH(B2, R{scope_data_start_row}:R{scope_data_start_row + len(unique_dates) * 20}, 0) + {i})="","",INDEX(R{scope_data_start_row + 1}:R{scope_data_start_row + len(unique_dates) * 20}, MATCH(B2, R{scope_data_start_row}:R{scope_data_start_row + len(unique_dates) * 20}, 0) + {i})), "")'
                name_cell = worksheet.cell(row=scope_start_row+3+i, column=scope_start_col, value=name_formula)
                name_cell.font = data_font
                name_cell.border = thin_border
                name_cell.alignment = left_alignment
                
                # Count formula - lookup based on selected date
                count_formula = f'=IFERROR(IF(INDEX(R{scope_data_start_row + 1}:R{scope_data_start_row + len(unique_dates) * 20}, MATCH(B2, R{scope_data_start_row}:R{scope_data_start_row + len(unique_dates) * 20}, 0) + {i})="","",INDEX(S{scope_data_start_row + 1}:S{scope_data_start_row + len(unique_dates) * 20}, MATCH(B2, R{scope_data_start_row}:R{scope_data_start_row + len(unique_dates) * 20}, 0) + {i})), "")'
                count_cell = worksheet.cell(row=scope_start_row+3+i, column=scope_start_col+1, value=count_formula)
                count_cell.font = data_font
                count_cell.border = thin_border
                count_cell.alignment = center_alignment
            
            # Hide the Scope data columns
            worksheet.column_dimensions['R'].hidden = True
            worksheet.column_dimensions['S'].hidden = True
            
            # Hide the Scope data rows
            for date_idx in range(len(unique_dates)):
                date_scope_start = scope_data_start_row + date_idx * 20
                for row_idx in range(date_scope_start, date_scope_start + 20):
                    worksheet.row_dimensions[row_idx].hidden = True
        else:
            no_audit_col_cell = worksheet.cell(row=scope_start_row+3, column=scope_start_col, value="Audit Date column not found")
            no_audit_col_cell.font = data_font
            no_audit_col_cell.border = thin_border
    else:
        no_col_cell = worksheet.cell(row=scope_start_row+3, column=scope_start_col, value="Scope column not found")
        no_col_cell.font = data_font
        no_col_cell.border = thin_border

    # Set column widths
    worksheet.column_dimensions['A'].width = 20  # Agent Name
    worksheet.column_dimensions['B'].width = 12  # Team Lead
    worksheet.column_dimensions['C'].width = 15  # Audited Claims
    worksheet.column_dimensions['D'].width = 10  # Error
    worksheet.column_dimensions['E'].width = 12  # Score
    worksheet.column_dimensions['F'].width = 12  # Productivity
    worksheet.column_dimensions['K'].width = 15  # QC Name
    worksheet.column_dimensions['L'].width = 10  # QC Count
    worksheet.column_dimensions['P'].width = 15  # Scope Name
    worksheet.column_dimensions['Q'].width = 10  # Scope Count
    
    # ========== ADD CHARTS SECTION ==========
    from openpyxl.chart import BarChart, PieChart, Reference
    
    # Chart 1: Simple Agent vs Errors (using existing data)
    chart1 = BarChart()
    chart1.title = "Agent vs Errors"
    chart1.x_axis.title = "Agents"
    chart1.y_axis.title = "Error Count"
    chart1.width = 15
    chart1.height = 10
    
    # Use existing daily data from main table (exclude total row)
    agents_ref = Reference(worksheet, min_col=1, min_row=6, max_row=6+len(daily_data)-2)
    errors_ref = Reference(worksheet, min_col=4, min_row=6, max_row=6+len(daily_data)-2)
    chart1.add_data(errors_ref, titles_from_data=False)
    chart1.set_categories(agents_ref)
    
    worksheet.add_chart(chart1, "V4")
    
    # Chart 2: Pareto Chart for Errors (80:20 Analysis)
    from openpyxl.chart import LineChart
    
    chart2 = BarChart()
    chart2.title = "Pareto Analysis - Error Distribution (80:20)"
    chart2.x_axis.title = "Error Categories"
    chart2.y_axis.title = "Error Count"
    chart2.width = 18
    chart2.height = 12
    
    error_category_col = None
    for col in full_data.columns:
        if 'error' in str(col).lower() and ('category' in str(col).lower() or 'type' in str(col).lower()):
            error_category_col = col
            break
    
    if error_category_col:
        pareto_start_row = scope_data_start_row + len(unique_dates) * 20 + 30
        
        for date_idx, date in enumerate(unique_dates):
            date_str = date.strftime('%Y-%m-%d')
            date_mask = pd.to_datetime(full_data[audit_date_col], errors='coerce').dt.date == date
            date_filtered_data = full_data[date_mask]
            
            if len(date_filtered_data) > 0:
                error_cat_data = date_filtered_data[error_category_col].dropna().astype(str)
                error_cat_data = error_cat_data[error_cat_data.str.strip() != '']
                error_cat_data = error_cat_data[error_cat_data != 'nan']
                
                if len(error_cat_data) > 0:
                    error_cat_counts = error_cat_data.value_counts().reset_index()
                    error_cat_counts.columns = ['Category', 'Count']
                    error_cat_counts = error_cat_counts.sort_values('Count', ascending=False)
                    
                    # Calculate cumulative percentage
                    total_errors = error_cat_counts['Count'].sum()
                    error_cat_counts['Cumulative'] = error_cat_counts['Count'].cumsum()
                    error_cat_counts['Cumulative%'] = (error_cat_counts['Cumulative'] / total_errors * 100).round(1)
                    
                    date_pareto_start = pareto_start_row + date_idx * 20
                    worksheet.cell(row=date_pareto_start, column=25, value=date_str)
                    
                    for i, cat_row in error_cat_counts.iterrows():
                        worksheet.cell(row=date_pareto_start + i + 1, column=25, value=str(cat_row['Category']))
                        worksheet.cell(row=date_pareto_start + i + 1, column=26, value=int(cat_row['Count']))
                        worksheet.cell(row=date_pareto_start + i + 1, column=27, value=float(cat_row['Cumulative%']))
        
        # Bar chart for error counts
        cat_ref = Reference(worksheet, min_col=25, min_row=pareto_start_row+1, max_row=pareto_start_row+10)
        count_ref = Reference(worksheet, min_col=26, min_row=pareto_start_row+1, max_row=pareto_start_row+10)
        chart2.add_data(count_ref, titles_from_data=False)
        chart2.set_categories(cat_ref)
        
        # Add cumulative percentage line
        line_chart = LineChart()
        line_chart.y_axis.axId = 200
        line_chart.y_axis.title = "Cumulative %"
        
        cumulative_ref = Reference(worksheet, min_col=27, min_row=pareto_start_row+1, max_row=pareto_start_row+10)
        line_chart.add_data(cumulative_ref, titles_from_data=False)
        line_chart.set_categories(cat_ref)
        
        # Add 80% reference line
        worksheet.cell(row=pareto_start_row+15, column=25, value="80% Line")
        worksheet.cell(row=pareto_start_row+15, column=27, value=80)
        
        chart2.y_axis.crosses = "max"
        chart2 += line_chart

    worksheet.add_chart(chart2, "AE4")
    
    # Chart 3: Scope-wise Audit Count (Bar Chart)
    chart3 = BarChart()
    chart3.title = "Scope-wise Audit Count"
    chart3.x_axis.title = "Scope"
    chart3.y_axis.title = "Count"
    chart3.width = 15
    chart3.height = 10
    
    scope_names_ref = Reference(worksheet, min_col=16, min_row=7, max_row=16)
    scope_counts_ref = Reference(worksheet, min_col=17, min_row=7, max_row=16)
    chart3.add_data(scope_counts_ref, titles_from_data=False)
    chart3.set_categories(scope_names_ref)
    worksheet.add_chart(chart3, "V22")
    
    # Hide the data columns
    worksheet.column_dimensions['G'].hidden = True
    worksheet.column_dimensions['H'].hidden = True
    worksheet.column_dimensions['I'].hidden = True
    worksheet.column_dimensions['J'].hidden = True
    worksheet.column_dimensions['M'].hidden = True



def prepare_daily_data(data):
    # Get unique dates and sort them
    unique_dates = sorted(data['Audit Date'].dt.date.unique())
    
    daily_summary = data.groupby(['Agent', 'Reporting To', data['Audit Date'].dt.date]).agg({
        'Audited transaction': 'sum', 
        'Has_Error': 'sum',
        'Sampling_Count': 'sum'
    }).reset_index()
    
    daily_summary['Score'] = np.where(
        daily_summary['Audited transaction'] > 0,
        ((daily_summary['Audited transaction'] - daily_summary['Has_Error']) / 
         daily_summary['Audited transaction'] * 100).round(2),
        100.0
    )
    
    agents = data[['Agent', 'Reporting To']].drop_duplicates()
    result_data = []
    
    for _, agent in agents.iterrows():
        row = {'Agent Name': agent['Agent'], 'Team Lead': agent['Reporting To']}
        
        for date in unique_dates:
            date_data = daily_summary[
                (daily_summary['Agent'] == agent['Agent']) & 
                (daily_summary['Audit Date'] == date)
            ]
            
            date_str = date.strftime('%Y-%m-%d')
            if len(date_data) > 0:
                row[f'{date_str}_Audited'] = int(date_data['Audited transaction'].iloc[0])
                row[f'{date_str}_Errors'] = int(date_data['Has_Error'].iloc[0])
                row[f'{date_str}_Score'] = float(date_data['Score'].iloc[0])
                row[f'{date_str}_Productivity'] = int(date_data['Sampling_Count'].iloc[0])
            else:
                row[f'{date_str}_Audited'] = 0
                row[f'{date_str}_Errors'] = 0
                row[f'{date_str}_Score'] = 100.00
                row[f'{date_str}_Productivity'] = 0
        
        result_data.append(row)
    
    # Add total row
    total_row = {'Agent Name': 'Total', 'Team Lead': ''}
    for date in unique_dates:
        date_total_data = daily_summary[daily_summary['Audit Date'] == date]
        total_audited = int(date_total_data['Audited transaction'].sum())
        total_errors = int(date_total_data['Has_Error'].sum())
        total_score = ((total_audited - total_errors) / total_audited * 100) if total_audited > 0 else 100.00
        
        date_str = date.strftime('%Y-%m-%d')
        total_productivity = int(date_total_data['Sampling_Count'].sum())
        total_row[f'{date_str}_Audited'] = total_audited
        total_row[f'{date_str}_Errors'] = total_errors
        total_row[f'{date_str}_Score'] = round(total_score, 2)
        total_row[f'{date_str}_Productivity'] = total_productivity
    
    result_data.append(total_row)
    return pd.DataFrame(result_data), unique_dates