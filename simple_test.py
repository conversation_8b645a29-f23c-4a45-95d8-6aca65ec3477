import pandas as pd
import openpyxl
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.utils import get_column_letter

def create_simple_test():
    """Create a simple test file with dropdowns"""
    
    # Create sample data with the exact columns from your file
    data = {
        'Scope': ['Therapy ABA', 'Therapy ABA', 'Therapy ABA'],
        'Processed Date': ['2025-07-28', '2025-07-28', '2025-07-28'],
        'Audit Date': ['2025-07-28', '2025-07-28', '2025-07-28'],
        'Practice': ['SIZA', 'SIZA', 'SIZA'],
        'Patient Name': ['Test Patient 1', 'Test Patient 2', 'Test Patient 3'],
        'DOS': ['2025-07-14', '2025-07-15', '2025-07-16'],
        'CPT': ['97153', '97153', '97153'],
        'Audited transaction': [1, 1, 1],
        'Error(Y/N)': ['No', '', ''],
        'Error field': ['-', '', ''],
        'Error Description': ['-', '', ''],
        'Error Category': ['-', '', ''],
        'Agent': ['Indhumathi R', 'Test Agent 2', 'Test Agent 3'],
        'Reporting To': ['Indhumathy P', 'Manager B', 'Manager C'],
        'QC Name': ['Pavithra R', 'QC Person 2', 'QC Person 3'],
        'Error': [0, 0, 0],
        "Week's": ['Week 31', 'Week 31', 'Week 31']
    }
    
    df = pd.DataFrame(data)
    
    # Save to Excel
    df.to_excel('simple_test_output.xlsx', sheet_name="Raw Data", index=False)
    
    # Reopen and add dropdowns
    workbook = openpyxl.load_workbook('simple_test_output.xlsx')
    add_dropdowns_simple(workbook, df)
    workbook.save('simple_test_output.xlsx')
    
    print("Simple test file created: simple_test_output.xlsx")
    print("Columns with dropdowns should be: Scope, Error(Y/N), Error field, Error Category, Agent, Reporting To, QC Name")

def add_dropdowns_simple(workbook, df):
    """Add dropdowns to the test file"""
    sheet = workbook["Raw Data"]
    
    # Define dropdown options
    dropdown_options = {
        'Scope': ['In Scope', 'Out of Scope'],
        'Error(Y/N)': ['Yes', 'No'],
        'Error field': ['Incorrect CPT', 'Incorrect DOS', 'Incorrect DX', 'Incorrect Auth#'],
        'Error Category': ['Typographical error', 'Negligence', 'Missed to check', 'Updates Missed']
    }
    
    # Find columns and add dropdowns
    for col_idx, col_name in enumerate(df.columns, 1):
        col_name_clean = str(col_name).strip()
        
        # Check if this column should have a dropdown
        dropdown_key = None
        if col_name_clean == 'Scope':
            dropdown_key = 'Scope'
        elif col_name_clean == 'Error(Y/N)':
            dropdown_key = 'Error(Y/N)'
        elif col_name_clean == 'Error field':
            dropdown_key = 'Error field'
        elif col_name_clean == 'Error Category':
            dropdown_key = 'Error Category'
        elif col_name_clean in ['Agent', 'Reporting To', 'QC Name']:
            # For these columns, create dropdown from existing data
            unique_values = df[col_name_clean].dropna().astype(str)
            unique_values = unique_values[unique_values.str.strip() != '']
            unique_values = sorted(unique_values.unique())
            
            if unique_values:
                options_str = ','.join(unique_values)
                dv = DataValidation(type="list", formula1=f'"{options_str}"', allow_blank=True)
                dv.error = f'Please select a valid {col_name_clean}'
                dv.errorTitle = f'Invalid {col_name_clean}'
                
                col_letter = get_column_letter(col_idx)
                data_range = f'{col_letter}2:{col_letter}{len(df) + 1}'
                
                sheet.add_data_validation(dv)
                dv.add(data_range)
                print(f"Added dynamic dropdown for {col_name_clean} in column {col_letter}")
        
        # Add predefined dropdowns
        if dropdown_key and dropdown_key in dropdown_options:
            options = dropdown_options[dropdown_key]
            options_str = ','.join(options)
            
            dv = DataValidation(type="list", formula1=f'"{options_str}"', allow_blank=True)
            dv.error = f'Please select a valid {dropdown_key} option'
            dv.errorTitle = f'Invalid {dropdown_key}'
            
            col_letter = get_column_letter(col_idx)
            data_range = f'{col_letter}2:{col_letter}{len(df) + 1}'
            
            sheet.add_data_validation(dv)
            dv.add(data_range)
            print(f"Added predefined dropdown for {dropdown_key} in column {col_letter}")

if __name__ == "__main__":
    create_simple_test()
