<!DOCTYPE html>
<html>
<head>
    <title>Audit Analysis Tool</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 600px; margin: 0 auto; padding: 40px 20px; }
        .card { background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); padding: 40px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; font-size: 2.5em; font-weight: 300; }
        .upload-area { border: 3px dashed #e0e0e0; padding: 60px 20px; text-align: center; margin: 30px 0; border-radius: 10px; transition: all 0.3s ease; cursor: pointer; }
        .upload-area:hover { border-color: #667eea; background: #f8f9ff; }
        .upload-area.dragover { border-color: #667eea; background: #f0f4ff; }
        .upload-icon { font-size: 4em; color: #ccc; margin-bottom: 20px; }
        .upload-text { color: #666; font-size: 1.1em; margin-bottom: 10px; }
        .upload-subtext { color: #999; font-size: 0.9em; }
        input[type="file"] { display: none; }
        .btn { background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 15px 40px; border: none; border-radius: 25px; cursor: pointer; font-size: 1.1em; font-weight: 500; transition: all 0.3s ease; width: 100%; margin-top: 20px; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .error { background: #ffe6e6; color: #d63031; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #d63031; }
        .file-info { background: #e8f5e8; color: #00b894; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #00b894; }
        .progress { width: 100%; height: 6px; background: #e0e0e0; border-radius: 3px; margin: 20px 0; overflow: hidden; display: none; }
        .progress-bar { height: 100%; background: linear-gradient(45deg, #667eea, #764ba2); width: 0%; transition: width 0.3s ease; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; margin: 30px 0; }
        .feature { text-align: center; padding: 20px; background: #f8f9ff; border-radius: 10px; }
        .feature-icon { font-size: 2em; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>📊 Audit Analysis Tool</h1>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">📈</div>
                    <div>Daily/Weekly/Monthly Reports</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎯</div>
                    <div>Error Analysis</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">📋</div>
                    <div>QC & Scope Summary</div>
                </div>
            </div>
            
            {% if error %}
                <div class="error">❌ {{ error }}</div>
            {% endif %}
            
            <form action="/upload" method="post" enctype="multipart/form-data" id="uploadForm">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">Drop your Excel file here or click to browse</div>
                    <div class="upload-subtext">Supports .xlsx and .xls files</div>
                    <input type="file" name="file" accept=".xlsx,.xls" required id="fileInput">
                </div>
                
                <div class="file-info" id="fileInfo" style="display: none;"></div>
                <div class="progress" id="progress">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                
                <button type="submit" class="btn" id="submitBtn">🚀 Upload & Process</button>
            </form>
        </div>
    </div>
    
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const submitBtn = document.getElementById('submitBtn');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');
        const form = document.getElementById('uploadForm');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFileInfo(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                showFileInfo(e.target.files[0]);
            }
        });
        
        function showFileInfo(file) {
            const size = (file.size / 1024 / 1024).toFixed(2);
            fileInfo.innerHTML = `📄 Selected: ${file.name} (${size} MB)`;
            fileInfo.style.display = 'block';
        }
        
        form.addEventListener('submit', (e) => {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '⏳ Processing...';
            progress.style.display = 'block';
            
            let width = 0;
            const interval = setInterval(() => {
                width += Math.random() * 10;
                if (width >= 90) {
                    clearInterval(interval);
                    width = 90;
                }
                progressBar.style.width = width + '%';
            }, 200);
        });
    </script>
</body>
</html>