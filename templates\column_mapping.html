<!DOCTYPE html>
<html>
<head>
    <title>Column Mapping</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Column Mapping Required</h2>
        
        <div class="alert alert-info">
            <strong>File:</strong> {{ filename }}<br>
            <strong>Total Rows:</strong> {{ total_rows }}<br>
            Please map the columns below to proceed with processing.
        </div>
        
        <form action="/process_mapping" method="post">
            <input type="hidden" name="filename" value="{{ filename }}">
            
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="audit_col" class="form-label">Audit Date Column</label>
                        <select class="form-select" name="audit_col" required>
                            <option value="">Select audit date column...</option>
                            {% for col in columns %}
                            <option value="{{ col }}" {% if detected.audit_col == col %}selected{% endif %}>{{ col }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="agent_col" class="form-label">Agent Column</label>
                        <select class="form-select" name="agent_col" required>
                            <option value="">Select agent column...</option>
                            {% for col in columns %}
                            <option value="{{ col }}" {% if detected.agent_col == col %}selected{% endif %}>{{ col }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="team_lead_col" class="form-label">Team Lead Column</label>
                        <select class="form-select" name="team_lead_col" required>
                            <option value="">Select team lead column...</option>
                            {% for col in columns %}
                            <option value="{{ col }}" {% if detected.team_lead_col == col %}selected{% endif %}>{{ col }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Process File</button>
                <a href="/" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
        
        <div class="mt-4">
            <h5>Available Columns:</h5>
            <div class="row">
                {% for col in columns %}
                <div class="col-md-3 mb-2">
                    <span class="badge bg-light text-dark">{{ col }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</body>
</html>